/**
 * 环境配置工具
 * 统一管理不同环境下的配置
 */

// 环境类型
export type Environment = 'development' | 'staging' | 'production' | 'test'

// 环境配置接口
export interface EnvConfig {
  apiBaseUrl: string
  apiPrefix: string
  debug: boolean
  environment: Environment
}

/**
 * 获取当前环境
 */
export function getCurrentEnvironment(): Environment {
  // 优先使用构建时的环境变量
  if (typeof process !== 'undefined' && process.env.NODE_ENV) {
    return process.env.NODE_ENV as Environment
  }
  
  // #ifdef H5
  // H5环境下可以通过URL判断
  if (typeof location !== 'undefined') {
    const hostname = location.hostname
    if (hostname.includes('staging') || hostname.includes('test')) {
      return 'staging'
    }
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return 'development'
    }
  }
  // #endif
  
  // 默认为开发环境
  return 'development'
}

/**
 * 获取环境配置
 */
export function getEnvConfig(): EnvConfig {
  const env = getCurrentEnvironment()
  
  // 基础配置
  const configs: Record<Environment, EnvConfig> = {
    development: {
      apiBaseUrl: 'http://127.0.0.1:3001',
      apiPrefix: '/api/v1/weixin',
      debug: true,
      environment: 'development'
    },
    staging: {
      apiBaseUrl: 'http://*************:3001',
      apiPrefix: '/api/v1/weixin',
      debug: false,
      environment: 'staging'
    },
    production: {
      apiBaseUrl: 'https://api.quhu.work',
      apiPrefix: '/api/v1/weixin',
      debug: false,
      environment: 'production'
    },
    test: {
      apiBaseUrl: 'http://127.0.0.1:3001',
      apiPrefix: '/api/v1/weixin',
      debug: true,
      environment: 'test'
    }
  }
  
  return configs[env]
}

/**
 * 是否为开发环境
 */
export function isDevelopment(): boolean {
  const env = getCurrentEnvironment()
  return env === 'development' || env === 'test'
}

/**
 * 是否为预发环境
 */
export function isStaging(): boolean {
  return getCurrentEnvironment() === 'staging'
}

/**
 * 是否为生产环境
 */
export function isProduction(): boolean {
  return getCurrentEnvironment() === 'production'
}

/**
 * 是否启用调试模式
 */
export function isDebugMode(): boolean {
  return getEnvConfig().debug
}

/**
 * 获取API基础URL
 */
export function getApiBaseUrl(): string {
  return getEnvConfig().apiBaseUrl
}

/**
 * 获取完整的API URL
 */
export function getApiUrl(path: string): string {
  const config = getEnvConfig()
  const baseUrl = config.apiBaseUrl
  const prefix = config.apiPrefix
  
  // 确保路径以 / 开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`
  
  return `${baseUrl}${prefix}${normalizedPath}`
}

/**
 * 记录环境信息
 */
export function logEnvironmentInfo(): void {
  const config = getEnvConfig()
  
  console.log('🌍 环境信息:')
  console.log(`  环境: ${config.environment}`)
  console.log(`  API地址: ${config.apiBaseUrl}`)
  console.log(`  API前缀: ${config.apiPrefix}`)
  console.log(`  调试模式: ${config.debug}`)
  
  // #ifdef H5
  if (typeof location !== 'undefined') {
    console.log(`  当前域名: ${location.hostname}`)
    console.log(`  当前端口: ${location.port}`)
  }
  // #endif
}
