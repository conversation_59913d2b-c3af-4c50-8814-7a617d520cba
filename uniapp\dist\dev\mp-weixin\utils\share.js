"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
const common_vendor = require("../common/vendor.js");
require("../api/request.js");
const api_weixin = require("../api/weixin.js");
class ShareUtils {
  /**
   * 获取分享配置
   */
  static async getShareConfig(params) {
    try {
      return await api_weixin.weixinApi.getShareConfig(params);
    } catch (error) {
      console.error("获取分享配置失败:", error);
      return {
        title: "英语单词游戏 - 挑战你的词汇量！",
        path: "/pages/index/index",
        imageUrl: "/static/share-logo.png",
        desc: "快来挑战英语单词游戏，提升你的词汇量！",
        summary: "和朋友一起学英语，看谁的词汇量更丰富！"
      };
    }
  }
  /**
   * 处理小程序分享（onShareAppMessage）
   * 根据官方文档实现
   */
  static async handleShareAppMessage(options, params) {
    try {
      console.log("处理小程序分享:", options);
      const shareConfig = await this.getShareConfig(params);
      const shareData = {
        title: shareConfig.title,
        path: shareConfig.path,
        imageUrl: shareConfig.imageUrl
      };
      if (shareConfig.query) {
        const separator = shareData.path.includes("?") ? "&" : "?";
        shareData.path += separator + shareConfig.query;
      }
      console.log("分享数据:", shareData);
      if (params == null ? void 0 : params.userId) {
        setTimeout(async () => {
          try {
            await this.claimShareReward({
              userId: params.userId,
              shareType: "app_message",
              page: params.page,
              levelId: params.levelId,
              timestamp: Date.now()
            });
          } catch (error) {
            console.error("获取分享奖励失败:", error);
          }
        }, 1e3);
      }
      return shareData;
    } catch (error) {
      console.error("处理分享失败:", error);
      return {
        title: "英语单词游戏",
        path: "/pages/index/index",
        imageUrl: "/static/share-logo.png"
      };
    }
  }
  /**
   * 检查今日是否已经获取过分享奖励
   */
  static checkDailyShareReward(userId) {
    try {
      const today = (/* @__PURE__ */ new Date()).toDateString();
      const storageKey = `daily_share_reward_${userId}_${today}`;
      const hasSharedToday = common_vendor.index.getStorageSync(storageKey);
      console.log(`检查每日分享奖励状态 - 用户: ${userId}, 日期: ${today}, 已分享: ${!!hasSharedToday}`);
      return !!hasSharedToday;
    } catch (error) {
      console.error("检查每日分享奖励状态失败:", error);
      return false;
    }
  }
  /**
   * 标记今日已获取分享奖励
   */
  static markDailyShareReward(userId) {
    try {
      const today = (/* @__PURE__ */ new Date()).toDateString();
      const storageKey = `daily_share_reward_${userId}_${today}`;
      common_vendor.index.setStorageSync(storageKey, true);
      console.log(`标记每日分享奖励完成 - 用户: ${userId}, 日期: ${today}`);
    } catch (error) {
      console.error("标记每日分享奖励失败:", error);
    }
  }
  /**
   * 获取分享奖励（带每日限制检查和防重复执行）
   */
  static async claimShareReward(params) {
    try {
      if (this.isClaimingReward) {
        console.log("分享奖励正在处理中，跳过重复请求");
        return;
      }
      const hasSharedToday = this.checkDailyShareReward(params.userId);
      if (hasSharedToday) {
        console.log("今日已获取过分享奖励，跳过本次请求");
        common_vendor.index.showToast({
          title: "今日已获得分享奖励",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      this.isClaimingReward = true;
      console.log("开始获取分享奖励:", params);
      const rewardResponse = await api_weixin.weixinApi.getShareReward();
      if (rewardResponse.success) {
        this.markDailyShareReward(params.userId);
        common_vendor.index.showToast({
          title: `获得${rewardResponse.reward.description}！`,
          icon: "success",
          duration: 3e3
        });
        console.log("分享奖励获取成功:", rewardResponse.reward);
      } else {
        console.log("分享奖励获取失败:", rewardResponse.message);
        if (rewardResponse.message.includes("今日") || rewardResponse.message.includes("已领取")) {
          this.markDailyShareReward(params.userId);
        }
      }
    } catch (error) {
      console.error("获取分享奖励失败:", error);
    } finally {
      this.isClaimingReward = false;
      console.log("分享奖励处理完成，重置状态");
    }
  }
  /**
   * App端分享到微信
   * 根据官方文档实现
   */
  static async shareToWeixin(options) {
    try {
      console.log("非App环境，无法使用uni.share");
      common_vendor.index.showToast({
        title: "当前环境不支持分享",
        icon: "none"
      });
    } catch (error) {
      console.error("App分享失败:", error);
      common_vendor.index.showToast({
        title: "分享失败",
        icon: "none"
      });
    }
  }
  /**
   * 系统分享
   * 根据官方文档实现
   */
  static async shareWithSystem(params) {
    try {
      const shareConfig = await this.getShareConfig(params);
      const shareOptions = {
        type: "text",
        summary: `${shareConfig.title}
${shareConfig.desc || shareConfig.summary}`,
        href: shareConfig.path,
        success: () => {
          console.log("系统分享成功");
        },
        fail: (err) => {
          console.error("系统分享失败:", err);
        }
      };
      common_vendor.index.shareWithSystem(shareOptions);
    } catch (error) {
      console.error("系统分享失败:", error);
      common_vendor.index.showToast({
        title: "分享失败",
        icon: "none"
      });
    }
  }
  /**
   * 显示分享菜单
   */
  static showShareMenu(options) {
    try {
      common_vendor.index.showShareMenu(options || {});
      console.log("显示分享菜单成功");
    } catch (error) {
      console.error("显示分享菜单失败:", error);
    }
  }
  /**
   * 隐藏分享菜单
   */
  static hideShareMenu() {
    try {
      common_vendor.index.hideShareMenu();
      console.log("隐藏分享菜单成功");
    } catch (error) {
      console.error("隐藏分享菜单失败:", error);
    }
  }
  /**
   * 生成分享链接
   */
  static generateSharePath(page, params) {
    let path = page.startsWith("/") ? page : `/${page}`;
    if (params && Object.keys(params).length > 0) {
      const queryString = Object.entries(params).map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`).join("&");
      path += `?${queryString}`;
    }
    return path;
  }
  /**
   * 解析分享链接参数
   */
  static parseShareParams(query) {
    const params = {};
    if (query) {
      const pairs = query.split("&");
      for (const pair of pairs) {
        const [key, value] = pair.split("=");
        if (key && value) {
          params[decodeURIComponent(key)] = decodeURIComponent(value);
        }
      }
    }
    return params;
  }
}
// 防重复执行的状态标记
__publicField(ShareUtils, "isClaimingReward", false);
const shareUtils = ShareUtils;
exports.shareUtils = shareUtils;
