"use strict";
const common_vendor = require("../../common/vendor.js");
const api_weixin = require("../../api/weixin.js");
const utils_audio = require("../../utils/audio.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const userInfo = common_vendor.ref(null);
    const dailyStatus = common_vendor.ref(null);
    const vipPackages = common_vendor.ref([]);
    const isLoadingVipPackages = common_vendor.ref(false);
    common_vendor.onLoad(async () => {
      const authModule = await "../../utils/auth.js";
      const isLoggedIn = await authModule.checkLoginAndRedirect({
        toastMessage: "请先登录以访问会员中心",
        redirectUrl: "/pages/login/index?redirect=" + encodeURIComponent("/pages/member-center/index")
      });
      if (!isLoggedIn) {
        return;
      }
      loadPageData();
    });
    const loadPageData = async () => {
      try {
        await loadUserInfo();
        await loadDailyStatus();
      } catch (error) {
        console.error("加载页面数据失败:", error);
      }
    };
    const loadUserInfo = async () => {
      try {
        const freshUserInfo = await api_weixin.weixinApi.refreshUserInfo();
        if (freshUserInfo) {
          userInfo.value = freshUserInfo;
        } else {
          const localUserInfo = api_weixin.weixinApi.getLocalUserInfo();
          userInfo.value = localUserInfo;
        }
      } catch (error) {
        console.error("加载用户信息失败:", error);
        const localUserInfo = api_weixin.weixinApi.getLocalUserInfo();
        userInfo.value = localUserInfo;
      }
    };
    const loadDailyStatus = async () => {
      try {
        const status = await api_weixin.weixinApi.getDailyStatus();
        dailyStatus.value = status;
      } catch (error) {
        console.error("加载每日状态失败:", error);
      }
    };
    const getUserInitial = () => {
      var _a, _b;
      const name = ((_a = userInfo.value) == null ? void 0 : _a.nickname) || ((_b = userInfo.value) == null ? void 0 : _b.maskedPhone) || "游戏玩家";
      return name.charAt(0).toUpperCase();
    };
    const showVipPackages = async () => {
      try {
        utils_audio.audioManager.playSoundEffect("click");
        isLoadingVipPackages.value = true;
        const packages = await api_weixin.weixinApi.getVipPackages();
        vipPackages.value = packages;
        const packageOptions = packages.map(
          (pkg) => `${pkg.name} - ¥${(pkg.price / 100).toFixed(2)}`
        );
        common_vendor.index.showActionSheet({
          itemList: packageOptions,
          success: (res) => {
            const selectedPackage = packages[res.tapIndex];
            if (selectedPackage) {
              showPaymentConfirm(selectedPackage);
            }
          }
        });
      } catch (error) {
        console.error("加载VIP套餐失败:", error);
        common_vendor.index.showToast({
          title: "加载套餐失败",
          icon: "none"
        });
      } finally {
        isLoadingVipPackages.value = false;
      }
    };
    const showPaymentConfirm = (vipPackage) => {
      const price = (vipPackage.price / 100).toFixed(2);
      common_vendor.index.showModal({
        title: "确认购买",
        content: `${vipPackage.name}
价格：¥${price}
时长：${vipPackage.duration}天

${vipPackage.description}`,
        showCancel: true,
        cancelText: "取消",
        confirmText: "立即支付",
        success: (res) => {
          if (res.confirm) {
            handleVipPayment(vipPackage);
          }
        }
      });
    };
    const handleVipPayment = async (vipPackage) => {
      try {
        common_vendor.index.showLoading({ title: "正在创建订单..." });
        const paymentSuccess = await api_weixin.weixinApi.requestPayment(vipPackage.id);
        if (paymentSuccess) {
          await loadUserInfo();
          await loadDailyStatus();
          common_vendor.index.showModal({
            title: "支付成功",
            content: `恭喜您成为VIP会员！
已获得${vipPackage.duration}天VIP特权`,
            showCancel: false,
            confirmText: "太棒了"
          });
        }
      } catch (error) {
        console.error("VIP支付失败:", error);
        common_vendor.index.showModal({
          title: "支付失败",
          content: "支付过程中出现问题，请稍后重试",
          showCancel: false,
          confirmText: "知道了"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    };
    return (_ctx, _cache) => {
      var _a, _b, _c, _d;
      return common_vendor.e({
        a: userInfo.value
      }, userInfo.value ? common_vendor.e({
        b: common_vendor.t(getUserInitial()),
        c: common_vendor.t(userInfo.value.nickname || userInfo.value.maskedPhone || "游戏玩家"),
        d: (_a = dailyStatus.value) == null ? void 0 : _a.isVip
      }, ((_b = dailyStatus.value) == null ? void 0 : _b.isVip) ? {} : {}) : {}, {
        e: (_c = dailyStatus.value) == null ? void 0 : _c.isVip
      }, ((_d = dailyStatus.value) == null ? void 0 : _d.isVip) ? {} : {
        f: common_vendor.o(showVipPackages)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a17a8d98"]]);
wx.createPage(MiniProgramPage);
