"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
const common_vendor = require("../common/vendor.js");
const api_config = require("./config.js");
class HttpClient {
  constructor() {
    __publicField(this, "requestInterceptors", []);
    __publicField(this, "responseInterceptors", []);
    __publicField(this, "errorInterceptors", []);
  }
  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(interceptor) {
    this.requestInterceptors.push(interceptor);
  }
  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(interceptor) {
    this.responseInterceptors.push(interceptor);
  }
  /**
   * 添加错误拦截器
   */
  addErrorInterceptor(interceptor) {
    this.errorInterceptors.push(interceptor);
  }
  /**
   * 执行请求拦截器
   */
  async executeRequestInterceptors(config) {
    let processedConfig = config;
    for (const interceptor of this.requestInterceptors) {
      processedConfig = await interceptor(processedConfig);
    }
    return processedConfig;
  }
  /**
   * 执行响应拦截器
   */
  async executeResponseInterceptors(response) {
    let processedResponse = response;
    for (const interceptor of this.responseInterceptors) {
      processedResponse = await interceptor(processedResponse);
    }
    return processedResponse;
  }
  /**
   * 执行错误拦截器
   */
  async executeErrorInterceptors(error) {
    let processedError = error;
    for (const interceptor of this.errorInterceptors) {
      processedError = await interceptor(processedError);
    }
    return processedError;
  }
  /**
   * 发送HTTP请求
   */
  async request(config) {
    try {
      const processedConfig = await this.executeRequestInterceptors(config);
      const response = await this.sendRequest(processedConfig);
      const processedResponse = await this.executeResponseInterceptors(response);
      return processedResponse.data;
    } catch (error) {
      const processedError = await this.executeErrorInterceptors(error);
      throw processedError;
    }
  }
  /**
   * 实际发送请求的方法
   */
  async sendRequest(config) {
    const {
      url,
      method = "GET",
      data,
      header = {},
      timeout = api_config.API_CONFIG.TIMEOUT,
      retry = api_config.API_CONFIG.RETRY_COUNT
    } = config;
    let lastError = null;
    for (let attempt = 0; attempt <= retry; attempt++) {
      try {
        const response = await new Promise((resolve, reject) => {
          common_vendor.index.request({
            url,
            method,
            data,
            header: {
              "Content-Type": "application/json",
              ...header
            },
            timeout,
            success: (res) => {
              resolve(res);
            },
            fail: (err) => {
              reject(this.createError(err.errMsg || api_config.ERROR_MESSAGES.NETWORK_ERROR, 0));
            }
          });
        });
        if (response.statusCode >= 200 && response.statusCode < 300) {
          return response;
        } else {
          throw this.createError(
            this.getErrorMessage(response.statusCode),
            response.statusCode,
            response.data
          );
        }
      } catch (error) {
        lastError = error;
        if (attempt === retry) {
          break;
        }
        await this.delay(api_config.API_CONFIG.RETRY_DELAY * (attempt + 1));
      }
    }
    throw lastError;
  }
  /**
   * 创建错误对象
   */
  createError(message, statusCode, details) {
    return {
      message,
      statusCode,
      details,
      error: statusCode ? `HTTP ${statusCode}` : "Request Error"
    };
  }
  /**
   * 根据状态码获取错误消息
   */
  getErrorMessage(statusCode) {
    switch (statusCode) {
      case api_config.HTTP_STATUS.BAD_REQUEST:
        return api_config.ERROR_MESSAGES.BAD_REQUEST;
      case api_config.HTTP_STATUS.UNAUTHORIZED:
        return api_config.ERROR_MESSAGES.UNAUTHORIZED;
      case api_config.HTTP_STATUS.NOT_FOUND:
        return api_config.ERROR_MESSAGES.NOT_FOUND;
      case api_config.HTTP_STATUS.INTERNAL_SERVER_ERROR:
        return api_config.ERROR_MESSAGES.SERVER_ERROR;
      default:
        return api_config.ERROR_MESSAGES.UNKNOWN_ERROR;
    }
  }
  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
  /**
   * GET请求
   */
  get(url, config) {
    return this.request({
      url,
      method: "GET",
      ...config
    });
  }
  /**
   * POST请求
   */
  post(url, data, config) {
    return this.request({
      url,
      method: "POST",
      data,
      ...config
    });
  }
  /**
   * PUT请求
   */
  put(url, data, config) {
    return this.request({
      url,
      method: "PUT",
      data,
      ...config
    });
  }
  /**
   * DELETE请求
   */
  delete(url, config) {
    return this.request({
      url,
      method: "DELETE",
      ...config
    });
  }
  /**
   * PATCH请求
   */
  patch(url, data, config) {
    return this.request({
      url,
      method: "PATCH",
      data,
      ...config
    });
  }
}
const httpClient = new HttpClient();
httpClient.addRequestInterceptor((config) => {
  console.log("发送请求:", config.method, config.url);
  return config;
});
httpClient.addResponseInterceptor((response) => {
  console.log("收到响应:", response.statusCode, response.data);
  return response;
});
httpClient.addErrorInterceptor(async (error) => {
  console.error("请求错误:", error);
  common_vendor.index.showToast({
    title: error.message,
    icon: "none",
    duration: 2e3
  });
  return error;
});
exports.httpClient = httpClient;
