<template>
  <view class="debug-container">
    <view class="debug-header">
      <text class="debug-title">API调试页面</text>
    </view>

    <view class="debug-section">
      <text class="section-title">当前 OpenID</text>
      <view class="info-card">
        <text class="info-text">{{ currentOpenid }}</text>
        <button class="btn-small" @click="refreshOpenid">刷新</button>
      </view>
    </view>

    <view class="debug-section">
      <text class="section-title">设置 OpenID</text>
      <view class="input-group">
        <input 
          class="input-field" 
          v-model="newOpenid" 
          placeholder="输入新的 openid"
        />
        <button class="btn-primary" @click="setNewOpenid">设置</button>
      </view>
    </view>

    <view class="debug-section">
      <text class="section-title">API测试</text>
      <view class="button-group">
        <button class="btn-test" @click="testWeixinLoginCode">测试获取微信code</button>
        <button class="btn-test" @click="testCheckSession">检查登录状态</button>
        <button class="btn-test" @click="testWeixinLogin">测试完整登录流程</button>
        <button class="btn-test" @click="testBindPhone">测试手机号绑定</button>
        <button class="btn-test" @click="testCheckConfig">检查微信配置</button>
        <button class="btn-test" @click="testGetUserInfo">获取用户信息</button>
        <button class="btn-test" @click="testGetlevel">获取关卡列表</button>
        <button class="btn-test" @click="testGetLevelDetail">获取关卡详情</button>
        <button class="btn-test" @click="testCompleteLevel">测试通关接口</button>
        <button class="btn-test" @click="testStartGame">测试开始游戏</button>
        <button class="btn-test" @click="testGetShareConfig">获取分享配置</button>
        <button class="btn-test" @click="testShareToWeixin">测试微信分享</button>
        <button class="btn-test" @click="testSystemShare">测试系统分享</button>
        <button class="btn-test" @click="testGetShareReward">获取分享奖励</button>
        <button class="btn-test" @click="testCheckDailyShare">检查每日分享状态</button>
        <button class="btn-test" @click="testResetDailyShare">重置每日分享状态</button>
        <button class="btn-test" @click="testMultipleShareReward">测试防重复执行</button>
        <button class="btn-test" @click="testDailyStatus">测试每日状态</button>
        <button class="btn-test" @click="testNewShareAPI">测试新分享API</button>
        <button class="btn-test" @click="testVipPackages">测试VIP套餐</button>
        <button class="btn-test" @click="testVipPayment">测试VIP支付</button>
        <button class="btn-test" @click="goToIconTest">测试uni-icons图标</button>
        <button class="btn-test" @click="goToAudioTest">测试音频功能</button>
      </view>
    </view>

    <view class="debug-section">
      <text class="section-title">关卡测试参数</text>
      <view class="input-group">
        <input
          class="input-field"
          v-model="testLevelId"
          placeholder="输入关卡ID (如: level-uuid-123)"
        />
        <input
          class="input-field"
          v-model="testUserId"
          placeholder="输入用户ID (如: 12345678)"
        />
      </view>
    </view>

    <view class="debug-section">
      <text class="section-title">响应结果</text>
      <view class="result-card">
        <text class="result-text">{{ apiResult }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { weixinApi } from '../../api'
import { shareUtils } from '../../utils/share'

const currentOpenid = ref('')
const newOpenid = ref('')
const testLevelId = ref('level-uuid-123')
const testUserId = ref('12345678')
const apiResult = ref('等待API调用...')

onMounted(() => {
  refreshOpenid()
})

const refreshOpenid = () => {
  currentOpenid.value = weixinApi.getOpenid()
}

const setNewOpenid = () => {
  if (newOpenid.value.trim()) {
    weixinApi.setOpenid(newOpenid.value.trim())
    refreshOpenid()
    newOpenid.value = ''
    uni.showToast({
      title: 'OpenID 已设置',
      icon: 'success'
    })
  }
}

const testGetUserInfo = async () => {
  try {
    apiResult.value = '正在获取用户信息...'
    const userInfo = await weixinApi.getUserInfo()
    apiResult.value = JSON.stringify(userInfo, null, 2)
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

const testGetlevel = async () => {
  try {
    apiResult.value = '正在获取关卡列表...'
    const level = await weixinApi.getlevel()
    apiResult.value = JSON.stringify(level, null, 2)
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

const testWeixinLoginCode = async () => {
  try {
    apiResult.value = '正在获取微信登录凭证...'
    const loginResult = await weixinApi.weixinLogin()
    apiResult.value = JSON.stringify(loginResult, null, 2)
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

const testCheckSession = async () => {
  try {
    apiResult.value = '正在检查登录状态...'
    const isValid = await weixinApi.checkSession()
    apiResult.value = JSON.stringify({ isValid, message: isValid ? '登录状态有效' : '登录状态已过期' }, null, 2)
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

const testWeixinLogin = async () => {
  try {
    apiResult.value = '正在执行完整微信登录流程（只使用uni.login）...'
    const loginResponse = await weixinApi.performWeixinLogin({
      phone: '',
      nickname: '测试用户',
      avatarUrl: ''
    })
    apiResult.value = JSON.stringify(loginResponse, null, 2)
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

const testBindPhone = async () => {
  try {
    apiResult.value = '正在绑定手机号...'
    const userInfo = await weixinApi.bindPhone({
      openid: weixinApi.getOpenid(),
      phone: '',
      nickname: '测试用户',
      avatarUrl: 'https://via.placeholder.com/100x100'
    })
    apiResult.value = JSON.stringify(userInfo, null, 2)
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

const testCheckConfig = async () => {
  try {
    apiResult.value = '正在检查微信配置...'
    const config = await weixinApi.checkWeixinConfig()
    apiResult.value = JSON.stringify(config, null, 2)
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

const testGetLevelDetail = async () => {
  try {
    if (!testLevelId.value.trim()) {
      apiResult.value = '请输入关卡ID'
      return
    }
    apiResult.value = '正在获取关卡详情...'
    const levelDetail = await weixinApi.getLevelDetail(testLevelId.value.trim())
    apiResult.value = JSON.stringify(levelDetail, null, 2)
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

const testCompleteLevel = async () => {
  try {
    if (!testUserId.value.trim() || !testLevelId.value.trim()) {
      apiResult.value = '请输入用户ID和关卡ID'
      return
    }
    apiResult.value = '正在调用通关接口...'
    const userInfo = await weixinApi.completeLevel(testUserId.value.trim(), testLevelId.value.trim())
    apiResult.value = JSON.stringify(userInfo, null, 2)
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

const testStartGame = async () => {
  try {
    if (!testUserId.value.trim() || !testLevelId.value.trim()) {
      apiResult.value = '请输入用户ID和关卡ID'
      return
    }
    apiResult.value = '正在调用开始游戏接口...'
    const userInfo = await weixinApi.startGame(testUserId.value.trim(), testLevelId.value.trim())
    apiResult.value = JSON.stringify(userInfo, null, 2)
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

const testGetShareConfig = async () => {
  try {
    apiResult.value = '正在获取分享配置...'
    const shareConfig = await weixinApi.getShareConfig({
      page: 'pages/index/index',
      levelId: testLevelId.value.trim() || undefined,
      userId: testUserId.value.trim() || undefined
    })
    apiResult.value = JSON.stringify(shareConfig, null, 2)
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

const testShareToWeixin = async () => {
  try {
    apiResult.value = '正在测试微信分享...'
    await shareUtils.shareToWeixin({
      scene: 'WXSceneSession',
      type: 0,
      params: {
        page: 'pages/index/index',
        levelId: testLevelId.value.trim() || undefined,
        userId: testUserId.value.trim() || undefined
      }
    })
    apiResult.value = '微信分享调用成功（请查看微信分享界面）'
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

const testSystemShare = async () => {
  try {
    apiResult.value = '正在测试系统分享...'
    await shareUtils.shareWithSystem({
      page: 'pages/index/index',
      levelId: testLevelId.value.trim() || undefined,
      userId: testUserId.value.trim() || undefined
    })
    apiResult.value = '系统分享调用成功（请查看系统分享界面）'
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

const testGetShareReward = async () => {
  try {
    if (!testUserId.value.trim()) {
      apiResult.value = '请输入用户ID'
      return
    }

    apiResult.value = '正在获取分享奖励...'
    const rewardResponse = await weixinApi.getShareReward({
      userId: testUserId.value.trim(),
      shareType: 'app_message',
      page: 'pages/index/index',
      levelId: testLevelId.value.trim() || undefined,
      timestamp: Date.now()
    })

    apiResult.value = JSON.stringify(rewardResponse, null, 2)

    if (rewardResponse.success) {
      uni.showToast({
        title: `获得${rewardResponse.reward.description}！`,
        icon: 'success',
        duration: 3000
      })
    }
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

const testCheckDailyShare = async () => {
  try {
    if (!testUserId.value.trim()) {
      apiResult.value = '请输入用户ID'
      return
    }

    const hasSharedToday = shareUtils.checkDailyShareReward(testUserId.value.trim())
    const today = new Date().toDateString()

    apiResult.value = JSON.stringify({
      userId: testUserId.value.trim(),
      date: today,
      hasSharedToday: hasSharedToday,
      message: hasSharedToday ? '今日已获取过分享奖励' : '今日尚未获取分享奖励'
    }, null, 2)

    uni.showToast({
      title: hasSharedToday ? '今日已分享' : '今日未分享',
      icon: hasSharedToday ? 'success' : 'none',
      duration: 2000
    })
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

const testResetDailyShare = async () => {
  try {
    if (!testUserId.value.trim()) {
      apiResult.value = '请输入用户ID'
      return
    }

    const today = new Date().toDateString()
    const storageKey = `daily_share_reward_${testUserId.value.trim()}_${today}`

    // 删除今日分享记录
    uni.removeStorageSync(storageKey)

    apiResult.value = JSON.stringify({
      userId: testUserId.value.trim(),
      date: today,
      action: '已重置今日分享状态',
      storageKey: storageKey
    }, null, 2)

    uni.showToast({
      title: '分享状态已重置',
      icon: 'success',
      duration: 2000
    })
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

const testMultipleShareReward = async () => {
  try {
    if (!testUserId.value.trim()) {
      apiResult.value = '请输入用户ID'
      return
    }

    apiResult.value = '正在测试防重复执行机制...\n'

    // 快速连续调用多次分享奖励
    const promises = []
    for (let i = 0; i < 5; i++) {
      promises.push(
        shareUtils.claimShareReward({
          userId: testUserId.value.trim(),
          shareType: 'app_message',
          page: 'pages/debug/index',
          timestamp: Date.now() + i
        })
      )
    }

    // 等待所有请求完成
    await Promise.all(promises)

    apiResult.value += '\n测试完成：已连续发起5次分享奖励请求\n'
    apiResult.value += '如果防重复机制正常工作，应该只有第一次请求被处理\n'
    apiResult.value += '其他请求应该被跳过并显示"正在处理中"的日志'

    uni.showToast({
      title: '防重复测试完成',
      icon: 'success',
      duration: 2000
    })
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

const testDailyStatus = async () => {
  try {
    if (!testUserId.value.trim()) {
      apiResult.value = '请输入用户ID'
      return
    }

    apiResult.value = '正在获取每日状态...\n'

    const dailyStatus = await weixinApi.getDailyStatus()

    apiResult.value = JSON.stringify({
      action: '获取每日状态',
      result: dailyStatus,
      analysis: {
        isVip: dailyStatus.isVip ? 'VIP用户，无限制' : '普通用户',
        unlockStatus: `${dailyStatus.dailyUnlockCount}/${dailyStatus.dailyUnlockLimit}`,
        remaining: dailyStatus.remainingUnlocks,
        canUnlock: dailyStatus.canUnlock,
        shareStatus: dailyStatus.dailyShared ? '今日已分享' : '今日未分享'
      }
    }, null, 2)

    uni.showToast({
      title: '每日状态获取成功',
      icon: 'success',
      duration: 2000
    })
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

const testNewShareAPI = async () => {
  try {
    if (!testUserId.value.trim()) {
      apiResult.value = '请输入用户ID'
      return
    }

    apiResult.value = '正在测试新分享API...\n'

    const shareResponse = await weixinApi.shareForReward()

    apiResult.value = JSON.stringify({
      action: '新分享API测试',
      result: shareResponse,
      analysis: {
        status: shareResponse.status,
        message: shareResponse.message,
        unlockInfo: shareResponse.status === 'success' ? {
          dailyUnlockCount: shareResponse.dailyUnlockCount,
          dailyUnlockLimit: shareResponse.dailyUnlockLimit,
          remainingUnlocks: shareResponse.remainingUnlocks,
          isVip: shareResponse.isVip,
          totalShares: shareResponse.totalShares
        } : null
      }
    }, null, 2)

    uni.showToast({
      title: '新分享API测试完成',
      icon: 'success',
      duration: 2000
    })
  } catch (error) {
    apiResult.value = `错误: ${(error as Error).message}`
  }
}

const testVipPackages = async () => {
  try {
    apiResult.value = '正在获取VIP套餐列表...\n'

    const vipPackages = await weixinApi.getVipPackages()

    apiResult.value = JSON.stringify({
      action: '获取VIP套餐列表',
      result: vipPackages,
      analysis: {
        packageCount: vipPackages.length,
        packages: vipPackages.map(pkg => ({
          id: pkg.id,
          name: pkg.name,
          price: `¥${(pkg.price / 100).toFixed(2)}`,
          duration: `${pkg.duration}天`,
          isActive: pkg.isActive
        }))
      }
    }, null, 2)

    uni.showToast({
      title: 'VIP套餐获取成功',
      icon: 'success',
      duration: 2000
    })
  } catch (error) {
    apiResult.value = `错误: ${(error as Error).message}`
  }
}

const testVipPayment = async () => {
  try {
    if (!testUserId.value.trim()) {
      apiResult.value = '请输入用户ID'
      return
    }

    apiResult.value = '正在测试VIP支付...\n'

    // 先获取VIP套餐列表
    const vipPackages = await weixinApi.getVipPackages()

    if (vipPackages.length === 0) {
      apiResult.value = '没有可用的VIP套餐'
      return
    }

    // 选择第一个套餐进行测试
    const testPackage = vipPackages[0]

    apiResult.value += `选择测试套餐: ${testPackage.name}\n`
    apiResult.value += `价格: ¥${(testPackage.price / 100).toFixed(2)}\n`
    apiResult.value += `时长: ${testPackage.duration}天\n\n`

    // 创建支付订单
    const paymentParams = await weixinApi.createPayment({
      openid: testUserId.value.trim(),
      packageId: testPackage.id
    })

    apiResult.value += '支付参数创建成功:\n'
    apiResult.value += JSON.stringify({
      action: '创建VIP支付订单',
      packageInfo: {
        id: testPackage.id,
        name: testPackage.name,
        price: testPackage.price,
        duration: testPackage.duration
      },
      paymentParams: {
        appId: paymentParams.appId,
        timeStamp: paymentParams.timeStamp,
        nonceStr: paymentParams.nonceStr,
        package: paymentParams.package,
        signType: paymentParams.signType,
        paySign: paymentParams.paySign ? '已生成' : '未生成'
      }
    }, null, 2)

    uni.showToast({
      title: 'VIP支付测试完成',
      icon: 'success',
      duration: 2000
    })
  } catch (error) {
    apiResult.value = `错误: ${(error as Error).message}`
  }
}

/**
 * 跳转到图标测试页面
 */
const goToIconTest = () => {
  uni.navigateTo({
    url: '/pages/test-icons/index'
  })
}

/**
 * 跳转到音频测试页面
 */
const goToAudioTest = () => {
  uni.navigateTo({
    url: '/pages/test-audio/index'
  })
}
</script>

<style scoped>
.debug-container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.debug-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.debug-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.debug-section {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.info-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.info-text {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  word-break: break-all;
}

.input-group {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.input-field {
  flex: 1;
  padding: 16rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.btn-small {
  padding: 8rpx 16rpx;
  font-size: 20rpx;
  background: #74b9ff;
  color: white;
  border: none;
  border-radius: 6rpx;
}

.btn-primary {
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  background: #74b9ff;
  color: white;
  border: none;
  border-radius: 8rpx;
}

.btn-test {
  padding: 20rpx;
  font-size: 26rpx;
  background: #00b894;
  color: white;
  border: none;
  border-radius: 8rpx;
}

.result-card {
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.result-text {
  font-size: 22rpx;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
