import { weixinApi } from '../api'
import type { ShareConfigRequest, ShareConfigResponse, ShareRewardRequest } from '../api/types'

/**
 * 分享工具类
 * 根据uniapp官方文档实现分享功能
 */
export class ShareUtils {

  // 防重复执行的状态标记
  private static isClaimingReward = false
  
  /**
   * 获取分享配置
   */
  static async getShareConfig(params?: ShareConfigRequest): Promise<ShareConfigResponse> {
    try {
      return await weixinApi.getShareConfig(params)
    } catch (error) {
      console.error('获取分享配置失败:', error)
      
      // 返回默认配置
      return {
        title: '英语单词游戏 - 挑战你的词汇量！',
        path: '/pages/index/index',
        imageUrl: '/static/share-logo.png',
        desc: '快来挑战英语单词游戏，提升你的词汇量！',
        summary: '和朋友一起学英语，看谁的词汇量更丰富！'
      }
    }
  }

  /**
   * 处理小程序分享（onShareAppMessage）
   * 根据官方文档实现
   */
  static async handleShareAppMessage(options: {
    from?: string
    target?: any
    webViewUrl?: string
  }, params?: ShareConfigRequest & { userId?: string }): Promise<any> {
    try {
      console.log('处理小程序分享:', options)

      // 获取分享配置
      const shareConfig = await this.getShareConfig(params)

      // 构建分享对象
      const shareData = {
        title: shareConfig.title,
        path: shareConfig.path,
        imageUrl: shareConfig.imageUrl
      }

      // 如果有查询参数，添加到path中
      if (shareConfig.query) {
        const separator = shareData.path.includes('?') ? '&' : '?'
        shareData.path += separator + shareConfig.query
      }

      console.log('分享数据:', shareData)

      // 分享成功后，异步获取奖励（不阻塞分享流程）
      if (params?.userId) {
        setTimeout(async () => {
          try {
            await this.claimShareReward({
              userId: params.userId!,
              shareType: 'app_message',
              page: params.page,
              levelId: params.levelId,
              timestamp: Date.now()
            })
          } catch (error) {
            console.error('获取分享奖励失败:', error)
          }
        }, 1000) // 延迟1秒，确保分享完成
      }

      return shareData
    } catch (error) {
      console.error('处理分享失败:', error)

      // 返回默认分享数据
      return {
        title: '英语单词游戏',
        path: '/pages/index/index',
        imageUrl: '/static/share-logo.png'
      }
    }
  }

  /**
   * 检查今日是否已经获取过分享奖励
   */
  static checkDailyShareReward(userId: string): boolean {
    try {
      const today = new Date().toDateString() // 获取今日日期字符串，如 "Mon Dec 25 2023"
      const storageKey = `daily_share_reward_${userId}_${today}`
      const hasSharedToday = uni.getStorageSync(storageKey)

      console.log(`检查每日分享奖励状态 - 用户: ${userId}, 日期: ${today}, 已分享: ${!!hasSharedToday}`)
      return !!hasSharedToday
    } catch (error) {
      console.error('检查每日分享奖励状态失败:', error)
      return false
    }
  }

  /**
   * 标记今日已获取分享奖励
   */
  static markDailyShareReward(userId: string): void {
    try {
      const today = new Date().toDateString()
      const storageKey = `daily_share_reward_${userId}_${today}`
      uni.setStorageSync(storageKey, true)

      console.log(`标记每日分享奖励完成 - 用户: ${userId}, 日期: ${today}`)
    } catch (error) {
      console.error('标记每日分享奖励失败:', error)
    }
  }

  /**
   * 获取分享奖励（带每日限制检查和防重复执行）
   */
  static async claimShareReward(params: ShareRewardRequest): Promise<void> {
    try {
      // 防止重复执行
      if (this.isClaimingReward) {
        console.log('分享奖励正在处理中，跳过重复请求')
        return
      }

      // 检查今日是否已经获取过分享奖励
      const hasSharedToday = this.checkDailyShareReward(params.userId)
      if (hasSharedToday) {
        console.log('今日已获取过分享奖励，跳过本次请求')
        uni.showToast({
          title: '今日已获得分享奖励',
          icon: 'none',
          duration: 2000
        })
        return
      }

      // 标记开始处理
      this.isClaimingReward = true
      console.log('开始获取分享奖励:', params)

      const rewardResponse = await weixinApi.getShareReward()

      if (rewardResponse.success) {
        // 标记今日已获取分享奖励
        this.markDailyShareReward(params.userId)

        // 显示奖励获得提示
        uni.showToast({
          title: `获得${rewardResponse.reward.description}！`,
          icon: 'success',
          duration: 3000
        })

        console.log('分享奖励获取成功:', rewardResponse.reward)
      } else {
        console.log('分享奖励获取失败:', rewardResponse.message)

        // 如果服务端返回今日已领取的消息，也标记本地状态
        if (rewardResponse.message.includes('今日') || rewardResponse.message.includes('已领取')) {
          this.markDailyShareReward(params.userId)
        }
      }
    } catch (error) {
      console.error('获取分享奖励失败:', error)
      // 不显示错误提示，避免影响用户体验
    } finally {
      // 重置处理状态
      this.isClaimingReward = false
      console.log('分享奖励处理完成，重置状态')
    }
  }

  /**
   * App端分享到微信
   * 根据官方文档实现
   */
  static async shareToWeixin(options: {
    scene: 'WXSceneSession' | 'WXSceneTimeline' | 'WXSceneFavorite'
    type?: number
    params?: ShareConfigRequest
  }): Promise<void> {
    try {
      // #ifdef APP-PLUS
      // 获取分享配置
      const shareConfig = await this.getShareConfig(options.params)
      
      // 构建分享参数
      const shareOptions = {
        provider: 'weixin',
        scene: options.scene,
        type: options.type || 0, // 默认图文分享
        title: shareConfig.title,
        summary: shareConfig.desc || shareConfig.summary,
        href: shareConfig.path,
        imageUrl: shareConfig.imageUrl,
        success: (res: any) => {
          console.log('分享成功:', res)
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          })
        },
        fail: (err: any) => {
          console.error('分享失败:', err)
          uni.showToast({
            title: '分享失败',
            icon: 'none'
          })
        }
      }

      // 调用分享
      uni.share(shareOptions)
      // #endif

      // #ifndef APP-PLUS
      console.log('非App环境，无法使用uni.share')
      uni.showToast({
        title: '当前环境不支持分享',
        icon: 'none'
      })
      // #endif
    } catch (error) {
      console.error('App分享失败:', error)
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      })
    }
  }

  /**
   * 系统分享
   * 根据官方文档实现
   */
  static async shareWithSystem(params?: ShareConfigRequest): Promise<void> {
    try {
      // 获取分享配置
      const shareConfig = await this.getShareConfig(params)
      
      // 构建分享参数
      const shareOptions = {
        type: 'text',
        summary: `${shareConfig.title}\n${shareConfig.desc || shareConfig.summary}`,
        href: shareConfig.path,
        success: () => {
          console.log('系统分享成功')
        },
        fail: (err: any) => {
          console.error('系统分享失败:', err)
        }
      }

      // 调用系统分享
      uni.shareWithSystem(shareOptions)
    } catch (error) {
      console.error('系统分享失败:', error)
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      })
    }
  }

  /**
   * 显示分享菜单
   */
  static showShareMenu(options?: {
    withShareTicket?: boolean
    title?: string
    content?: string
    imageUrl?: string
    path?: string
  }): void {
    try {
      // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
      uni.showShareMenu(options || {})
      console.log('显示分享菜单成功')
      // #endif

      // #ifndef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
      console.log('当前环境不支持显示分享菜单')
      // #endif
    } catch (error) {
      console.error('显示分享菜单失败:', error)
    }
  }

  /**
   * 隐藏分享菜单
   */
  static hideShareMenu(): void {
    try {
      // #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO || MP-QQ
      uni.hideShareMenu()
      console.log('隐藏分享菜单成功')
      // #endif

      // #ifndef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO || MP-QQ
      console.log('当前环境不支持隐藏分享菜单')
      // #endif
    } catch (error) {
      console.error('隐藏分享菜单失败:', error)
    }
  }

  /**
   * 生成分享链接
   */
  static generateSharePath(page: string, params?: Record<string, string>): string {
    let path = page.startsWith('/') ? page : `/${page}`
    
    if (params && Object.keys(params).length > 0) {
      const queryString = Object.entries(params)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join('&')
      
      path += `?${queryString}`
    }
    
    return path
  }

  /**
   * 解析分享链接参数
   */
  static parseShareParams(query: string): Record<string, string> {
    const params: Record<string, string> = {}
    
    if (query) {
      const pairs = query.split('&')
      for (const pair of pairs) {
        const [key, value] = pair.split('=')
        if (key && value) {
          params[decodeURIComponent(key)] = decodeURIComponent(value)
        }
      }
    }
    
    return params
  }
}

// 导出默认实例
export const shareUtils = ShareUtils
