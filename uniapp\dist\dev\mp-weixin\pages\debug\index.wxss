
.debug-container.data-v-14d3e3ed {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}
.debug-header.data-v-14d3e3ed {
  text-align: center;
  margin-bottom: 40rpx;
}
.debug-title.data-v-14d3e3ed {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.debug-section.data-v-14d3e3ed {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.section-title.data-v-14d3e3ed {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}
.info-card.data-v-14d3e3ed {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}
.info-text.data-v-14d3e3ed {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  word-break: break-all;
}
.input-group.data-v-14d3e3ed {
  display: flex;
  gap: 16rpx;
  align-items: center;
}
.input-field.data-v-14d3e3ed {
  flex: 1;
  padding: 16rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 24rpx;
}
.button-group.data-v-14d3e3ed {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.btn-small.data-v-14d3e3ed {
  padding: 8rpx 16rpx;
  font-size: 20rpx;
  background: #74b9ff;
  color: white;
  border: none;
  border-radius: 6rpx;
}
.btn-primary.data-v-14d3e3ed {
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  background: #74b9ff;
  color: white;
  border: none;
  border-radius: 8rpx;
}
.btn-test.data-v-14d3e3ed {
  padding: 20rpx;
  font-size: 26rpx;
  background: #00b894;
  color: white;
  border: none;
  border-radius: 8rpx;
}
.result-card.data-v-14d3e3ed {
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  max-height: 400rpx;
  overflow-y: auto;
}
.result-text.data-v-14d3e3ed {
  font-size: 22rpx;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
}
