/**
 * 统一的HTTP请求工具类
 * 封装uni.request，提供统一的错误处理、重试机制等
 */

import { API_CONFIG, HTTP_STATUS, ERROR_MESSAGES } from './config'
import type { RequestConfig, RequestResponse, ApiError } from './types'

// 请求拦截器类型
type RequestInterceptor = (config: RequestConfig) => RequestConfig | Promise<RequestConfig>
type ResponseInterceptor<T = any> = (response: RequestResponse<T>) => RequestResponse<T> | Promise<RequestResponse<T>>
type ErrorInterceptor = (error: ApiError) => Promise<ApiError>

class HttpClient {
  private requestInterceptors: RequestInterceptor[] = []
  private responseInterceptors: ResponseInterceptor[] = []
  private errorInterceptors: ErrorInterceptor[] = []

  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(interceptor: RequestInterceptor) {
    this.requestInterceptors.push(interceptor)
  }

  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(interceptor: ResponseInterceptor) {
    this.responseInterceptors.push(interceptor)
  }

  /**
   * 添加错误拦截器
   */
  addErrorInterceptor(interceptor: ErrorInterceptor) {
    this.errorInterceptors.push(interceptor)
  }

  /**
   * 执行请求拦截器
   */
  private async executeRequestInterceptors(config: RequestConfig): Promise<RequestConfig> {
    let processedConfig = config
    for (const interceptor of this.requestInterceptors) {
      processedConfig = await interceptor(processedConfig)
    }
    return processedConfig
  }

  /**
   * 执行响应拦截器
   */
  private async executeResponseInterceptors<T>(response: RequestResponse<T>): Promise<RequestResponse<T>> {
    let processedResponse = response
    for (const interceptor of this.responseInterceptors) {
      processedResponse = await interceptor(processedResponse)
    }
    return processedResponse
  }

  /**
   * 执行错误拦截器
   */
  private async executeErrorInterceptors(error: ApiError): Promise<ApiError> {
    let processedError = error
    for (const interceptor of this.errorInterceptors) {
      processedError = await interceptor(processedError)
    }
    return processedError
  }

  /**
   * 发送HTTP请求
   */
  async request<T = any>(config: RequestConfig): Promise<T> {
    try {
      // 执行请求拦截器
      const processedConfig = await this.executeRequestInterceptors(config)
      
      // 发送请求
      const response = await this.sendRequest<T>(processedConfig)
      
      // 执行响应拦截器
      const processedResponse = await this.executeResponseInterceptors(response)
      
      return processedResponse.data
    } catch (error) {
      // 执行错误拦截器
      const processedError = await this.executeErrorInterceptors(error as ApiError)
      throw processedError
    }
  }

  /**
   * 实际发送请求的方法
   */
  private async sendRequest<T>(config: RequestConfig): Promise<RequestResponse<T>> {
    const {
      url,
      method = 'GET',
      data,
      header = {},
      timeout = API_CONFIG.TIMEOUT,
      retry = API_CONFIG.RETRY_COUNT
    } = config

    let lastError: any = null

    // 重试机制
    for (let attempt = 0; attempt <= retry; attempt++) {
      try {
        const response = await new Promise<RequestResponse<T>>((resolve, reject) => {
          uni.request({
            url,
            method,
            data,
            header: {
              'Content-Type': 'application/json',
              ...header
            },
            timeout,
            success: (res) => {
              resolve(res as RequestResponse<T>)
            },
            fail: (err) => {
              reject(this.createError(err.errMsg || ERROR_MESSAGES.NETWORK_ERROR, 0))
            }
          })
        })

        // 检查HTTP状态码
        if (response.statusCode >= 200 && response.statusCode < 300) {
          return response
        } else {
          throw this.createError(
            this.getErrorMessage(response.statusCode),
            response.statusCode,
            response.data
          )
        }
      } catch (error) {
        lastError = error
        
        // 如果是最后一次尝试，直接抛出错误
        if (attempt === retry) {
          break
        }
        
        // 等待一段时间后重试
        await this.delay(API_CONFIG.RETRY_DELAY * (attempt + 1))
      }
    }

    throw lastError
  }

  /**
   * 创建错误对象
   */
  private createError(message: string, statusCode?: number, details?: any): ApiError {
    return {
      message,
      statusCode,
      details,
      error: statusCode ? `HTTP ${statusCode}` : 'Request Error'
    }
  }

  /**
   * 根据状态码获取错误消息
   */
  private getErrorMessage(statusCode: number): string {
    switch (statusCode) {
      case HTTP_STATUS.BAD_REQUEST:
        return ERROR_MESSAGES.BAD_REQUEST
      case HTTP_STATUS.UNAUTHORIZED:
        return ERROR_MESSAGES.UNAUTHORIZED
      case HTTP_STATUS.NOT_FOUND:
        return ERROR_MESSAGES.NOT_FOUND
      case HTTP_STATUS.INTERNAL_SERVER_ERROR:
        return ERROR_MESSAGES.SERVER_ERROR
      default:
        return ERROR_MESSAGES.UNKNOWN_ERROR
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * GET请求
   */
  get<T = any>(url: string, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: 'GET',
      ...config
    })
  }

  /**
   * POST请求
   */
  post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
      ...config
    })
  }

  /**
   * PUT请求
   */
  put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
      ...config
    })
  }

  /**
   * DELETE请求
   */
  delete<T = any>(url: string, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: 'DELETE',
      ...config
    })
  }

  /**
   * PATCH请求
   */
  patch<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: 'PATCH',
      data,
      ...config
    })
  }
}

// 创建默认的HTTP客户端实例
export const httpClient = new HttpClient()

// 添加默认的请求拦截器
httpClient.addRequestInterceptor((config) => {
  console.log('发送请求:', config.method, config.url)
  return config
})

// 添加默认的响应拦截器
httpClient.addResponseInterceptor((response) => {
  console.log('收到响应:', response.statusCode, response.data)
  return response
})

// 添加默认的错误拦截器
httpClient.addErrorInterceptor(async (error) => {
  console.error('请求错误:', error)
  
  // 显示错误提示
  uni.showToast({
    title: error.message,
    icon: 'none',
    duration: 2000
  })
  
  return error
})

export default httpClient
