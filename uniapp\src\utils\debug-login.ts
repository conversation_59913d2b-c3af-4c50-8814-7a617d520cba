/**
 * 微信登录调试工具
 * 用于诊断登录500错误的问题
 */

import { getApiUrl } from './env'
import httpClient from '../api/request'

export interface LoginDebugInfo {
  step: string
  success: boolean
  data?: any
  error?: any
  timestamp: string
}

export class LoginDebugger {
  private logs: LoginDebugInfo[] = []

  private log(step: string, success: boolean, data?: any, error?: any) {
    const logEntry: LoginDebugInfo = {
      step,
      success,
      data,
      error,
      timestamp: new Date().toISOString()
    }
    this.logs.push(logEntry)
    console.log(`[登录调试] ${step}:`, success ? '✅' : '❌', data || error)
  }

  /**
   * 测试环境配置
   */
  async testEnvironment(): Promise<boolean> {
    try {
      const apiUrl = getApiUrl('/login')
      this.log('环境配置检查', true, { apiUrl })
      return true
    } catch (error) {
      this.log('环境配置检查', false, null, error)
      return false
    }
  }

  /**
   * 测试网络连接
   */
  async testNetworkConnection(): Promise<boolean> {
    try {
      const baseUrl = getApiUrl('')
      const response = await httpClient.get(`${baseUrl}/config`)
      this.log('网络连接测试', true, { statusCode: response })
      return true
    } catch (error) {
      this.log('网络连接测试', false, null, error)
      return false
    }
  }

  /**
   * 测试微信登录接口
   */
  async testWeixinLogin(code: string): Promise<boolean> {
    try {
      const url = getApiUrl('/login')
      const loginRequest = {
        code: code,
        phone: '',
        nickname: '测试用户',
        avatarUrl: ''
      }

      this.log('发送登录请求', true, { url, request: loginRequest })

      const response = await httpClient.post(url, loginRequest)
      this.log('登录请求响应', true, response)
      return true
    } catch (error) {
      this.log('登录请求失败', false, null, error)
      return false
    }
  }

  /**
   * 完整的登录调试流程
   */
  async debugLogin(code: string): Promise<LoginDebugInfo[]> {
    console.log('🔍 开始微信登录调试...')
    
    // 清空之前的日志
    this.logs = []

    // 1. 测试环境配置
    await this.testEnvironment()

    // 2. 测试网络连接
    await this.testNetworkConnection()

    // 3. 测试微信登录
    await this.testWeixinLogin(code)

    console.log('🔍 登录调试完成，结果:')
    this.logs.forEach((log, index) => {
      console.log(`${index + 1}. ${log.step}: ${log.success ? '✅' : '❌'}`)
      if (!log.success && log.error) {
        console.error('   错误详情:', log.error)
      }
    })

    return this.logs
  }

  /**
   * 获取调试日志
   */
  getLogs(): LoginDebugInfo[] {
    return this.logs
  }

  /**
   * 导出调试报告
   */
  exportDebugReport(): string {
    const report = {
      timestamp: new Date().toISOString(),
      logs: this.logs,
      summary: {
        total: this.logs.length,
        success: this.logs.filter(log => log.success).length,
        failed: this.logs.filter(log => !log.success).length
      }
    }

    return JSON.stringify(report, null, 2)
  }
}

/**
 * 检查服务器状态
 */
export async function checkServerStatus(): Promise<{
  isOnline: boolean
  responseTime: number
  error?: string
}> {
  const startTime = Date.now()
  
  try {
    const url = getApiUrl('/config')
    await httpClient.get(url)
    
    return {
      isOnline: true,
      responseTime: Date.now() - startTime
    }
  } catch (error) {
    return {
      isOnline: false,
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : String(error)
    }
  }
}

/**
 * 分析错误类型
 */
export function analyzeError(error: any): {
  type: string
  description: string
  suggestions: string[]
} {
  if (!error) {
    return {
      type: 'unknown',
      description: '未知错误',
      suggestions: ['请重试']
    }
  }

  const errorMessage = error.message || String(error)
  const statusCode = error.statusCode

  if (statusCode === 500) {
    return {
      type: 'server_error',
      description: '服务器内部错误',
      suggestions: [
        '检查服务器是否正常运行',
        '检查数据库连接',
        '检查微信API配置',
        '查看服务器日志'
      ]
    }
  }

  if (statusCode === 400) {
    return {
      type: 'bad_request',
      description: '请求参数错误',
      suggestions: [
        '检查微信code是否有效',
        '检查请求参数格式',
        '确认code未被重复使用'
      ]
    }
  }

  if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
    return {
      type: 'network_error',
      description: '网络连接错误',
      suggestions: [
        '检查网络连接',
        '检查服务器地址配置',
        '重试请求'
      ]
    }
  }

  if (errorMessage.includes('code')) {
    return {
      type: 'code_error',
      description: '微信code相关错误',
      suggestions: [
        '重新获取微信code',
        '检查code是否过期',
        '确认code格式正确'
      ]
    }
  }

  return {
    type: 'unknown',
    description: errorMessage,
    suggestions: ['请联系技术支持']
  }
}
