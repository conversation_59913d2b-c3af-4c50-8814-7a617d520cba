/**
 * 开发环境检测工具
 * 用于判断当前是否为开发环境，决定是否显示调试功能
 */

import { isDevelopment, isStaging, getCurrentEnvironment, logEnvironmentInfo } from './env'

/**
 * 检测当前是否为开发环境
 * @returns {boolean} 是否为开发环境
 */
export function isDevelopmentEnvironment(): boolean {
  try {
    // 使用统一的环境检测
    const currentEnv = getCurrentEnvironment()
    const isDevEnv = isDevelopment() || isStaging()

    // #ifdef MP-WEIXIN
    // 微信小程序开发工具检测
    const accountInfo = uni.getAccountInfoSync()
    const envVersion = accountInfo.miniProgram.envVersion
    const isMpDev = envVersion === 'develop' || envVersion === 'trial'
    console.log('微信小程序环境:', envVersion, '是否开发环境:', isMpDev)
    // 微信小程序以小程序环境为准，但也考虑构建环境
    return isMpDev || isDevEnv
    // #endif

    // #ifdef H5
    // H5环境检测
    console.log('H5环境:', {
      currentEnv: currentEnv,
      isDevelopment: isDevelopment(),
      isStaging: isStaging(),
      hostname: typeof location !== 'undefined' ? location.hostname : 'unknown',
      port: typeof location !== 'undefined' ? location.port : 'unknown'
    })
    return isDevEnv
    // #endif
    
    // #ifdef APP-PLUS
    // App开发环境检测
    const isDev = plus.runtime.isDebugMode
    console.log('App环境，调试模式:', isDev)
    return isDev
    // #endif
    
    // #ifdef MP-ALIPAY
    // 支付宝小程序开发环境检测
    const isDev = my.getSystemInfoSync().platform === 'devtools'
    console.log('支付宝小程序环境，是否开发工具:', isDev)
    return isDev
    // #endif
    
    // #ifdef MP-BAIDU
    // 百度小程序开发环境检测
    const isDev = swan.getSystemInfoSync().platform === 'devtools'
    console.log('百度小程序环境，是否开发工具:', isDev)
    return isDev
    // #endif
    
    // #ifdef MP-TOUTIAO
    // 字节跳动小程序开发环境检测
    const isDev = tt.getSystemInfoSync().platform === 'devtools'
    console.log('字节跳动小程序环境，是否开发工具:', isDev)
    return isDev
    // #endif
    
    // #ifdef MP-QQ
    // QQ小程序开发环境检测
    const isDev = qq.getSystemInfoSync().platform === 'devtools'
    console.log('QQ小程序环境，是否开发工具:', isDev)
    return isDev
    // #endif
    
    // 默认情况（其他平台）
    console.log('未知平台，默认非开发环境')
    return false
  } catch (error) {
    console.error('检测开发环境失败:', error)
    // 默认不显示调试按钮
    return false
  }
}

/**
 * 获取当前运行环境信息
 * @returns {object} 环境信息对象
 */
export function getEnvironmentInfo(): {
  platform: string
  isDevelopment: boolean
  environment: string
  envVersion?: string
  hostname?: string
  port?: string
} {
  try {
    const isDev = isDevelopmentEnvironment()
    const currentEnv = getCurrentEnvironment()

    // #ifdef MP-WEIXIN
    const accountInfo = uni.getAccountInfoSync()
    return {
      platform: 'MP-WEIXIN',
      isDevelopment: isDev,
      environment: currentEnv,
      envVersion: accountInfo.miniProgram.envVersion
    }
    // #endif

    // #ifdef H5
    return {
      platform: 'H5',
      isDevelopment: isDev,
      environment: currentEnv,
      hostname: typeof location !== 'undefined' ? location.hostname : 'unknown',
      port: typeof location !== 'undefined' ? location.port : 'unknown'
    }
    // #endif
    
    // #ifdef APP-PLUS
    return {
      platform: 'APP-PLUS',
      isDevelopment: isDev
    }
    // #endif
    
    // #ifdef MP-ALIPAY
    return {
      platform: 'MP-ALIPAY',
      isDevelopment: isDev
    }
    // #endif
    
    // #ifdef MP-BAIDU
    return {
      platform: 'MP-BAIDU',
      isDevelopment: isDev
    }
    // #endif
    
    // #ifdef MP-TOUTIAO
    return {
      platform: 'MP-TOUTIAO',
      isDevelopment: isDev
    }
    // #endif
    
    // #ifdef MP-QQ
    return {
      platform: 'MP-QQ',
      isDevelopment: isDev
    }
    // #endif
    
    // 默认情况
    return {
      platform: 'UNKNOWN',
      isDevelopment: false,
      environment: currentEnv
    }
  } catch (error) {
    console.error('获取环境信息失败:', error)
    return {
      platform: 'ERROR',
      isDevelopment: false,
      environment: 'unknown'
    }
  }
}

/**
 * 是否应该显示调试功能
 * 可以根据需要添加更多条件
 * @returns {boolean} 是否显示调试功能
 */
export function shouldShowDebugFeatures(): boolean {
  const isDev = isDevelopmentEnvironment()
  
  // 可以添加更多条件，比如特定用户、特定时间等
  // const isTestUser = checkIfTestUser()
  // const isInternalBuild = checkIfInternalBuild()
  
  return isDev
}

/**
 * 调试日志输出
 * 只在开发环境输出
 * @param {string} message 日志消息
 * @param {any} data 日志数据
 */
export function debugLog(message: string, data?: any): void {
  if (isDevelopmentEnvironment()) {
    if (data !== undefined) {
      console.log(`[DEBUG] ${message}`, data)
    } else {
      console.log(`[DEBUG] ${message}`)
    }
  }
}

/**
 * 调试错误输出
 * 只在开发环境输出
 * @param {string} message 错误消息
 * @param {any} error 错误对象
 */
export function debugError(message: string, error?: any): void {
  if (isDevelopmentEnvironment()) {
    if (error !== undefined) {
      console.error(`[DEBUG ERROR] ${message}`, error)
    } else {
      console.error(`[DEBUG ERROR] ${message}`)
    }
  }
}

/**
 * 调试警告输出
 * 只在开发环境输出
 * @param {string} message 警告消息
 * @param {any} data 警告数据
 */
export function debugWarn(message: string, data?: any): void {
  if (isDevelopmentEnvironment()) {
    if (data !== undefined) {
      console.warn(`[DEBUG WARN] ${message}`, data)
    } else {
      console.warn(`[DEBUG WARN] ${message}`)
    }
  }
}

/**
 * 开发环境专用的性能测试
 * @param {string} label 测试标签
 * @param {Function} fn 要测试的函数
 * @returns {any} 函数执行结果
 */
export async function debugPerformance<T>(label: string, fn: () => Promise<T> | T): Promise<T> {
  if (!isDevelopmentEnvironment()) {
    return await fn()
  }
  
  const startTime = performance.now()
  console.time(`[PERF] ${label}`)
  
  try {
    const result = await fn()
    const endTime = performance.now()
    console.timeEnd(`[PERF] ${label}`)
    console.log(`[PERF] ${label} 执行时间: ${(endTime - startTime).toFixed(2)}ms`)
    return result
  } catch (error) {
    const endTime = performance.now()
    console.timeEnd(`[PERF] ${label}`)
    console.error(`[PERF ERROR] ${label} 执行失败，耗时: ${(endTime - startTime).toFixed(2)}ms`, error)
    throw error
  }
}

// 导出默认的开发环境检测函数
export default isDevelopmentEnvironment
