/**
 * 登录状态管理工具
 * 用于检查登录状态、处理登录跳转等
 */

import { weixinApi } from '@/api'
import type { UserInfo } from '@/api/types'

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export function isUserLoggedIn(): boolean {
  try {
    // 检查本地存储的用户信息
    const userInfo = weixinApi.getLocalUserInfo()
    const openid = weixinApi.getOpenid()
    
    // 用户信息和openid都存在且有效
    return !!(userInfo && openid && openid !== 'openid')
  } catch (error) {
    console.error('检查登录状态失败:', error)
    return false
  }
}

/**
 * 获取当前登录用户信息
 * @returns {UserInfo | null} 用户信息
 */
export function getCurrentUser(): UserInfo | null {
  try {
    return weixinApi.getLocalUserInfo()
  } catch (error) {
    console.error('获取当前用户信息失败:', error)
    return null
  }
}

/**
 * 检查登录状态，如果未登录则跳转到登录页面
 * @param options 配置选项
 * @returns {Promise<boolean>} 是否已登录
 */
export async function checkLoginAndRedirect(options?: {
  showToast?: boolean
  toastMessage?: string
  redirectUrl?: string
}): Promise<boolean> {
  const {
    showToast = true,
    toastMessage = '请先登录',
    redirectUrl = '/pages/login/index'
  } = options || {}

  if (isUserLoggedIn()) {
    return true
  }

  // 显示提示
  if (showToast) {
    uni.showToast({
      title: toastMessage,
      icon: 'none',
      duration: 2000
    })
  }

  // 延迟跳转到登录页面
  setTimeout(() => {
    uni.navigateTo({
      url: redirectUrl
    })
  }, 1000)

  return false
}

/**
 * 执行需要登录的操作
 * @param action 需要执行的操作
 * @param options 配置选项
 */
export async function requireLogin<T>(
  action: () => Promise<T> | T,
  options?: {
    showToast?: boolean
    toastMessage?: string
    redirectUrl?: string
  }
): Promise<T | null> {
  const isLoggedIn = await checkLoginAndRedirect(options)
  
  if (!isLoggedIn) {
    return null
  }

  try {
    return await action()
  } catch (error) {
    console.error('执行需要登录的操作失败:', error)
    throw error
  }
}

/**
 * 刷新登录状态
 * @returns {Promise<boolean>} 刷新是否成功
 */
export async function refreshLoginStatus(): Promise<boolean> {
  try {
    // 检查会话是否有效
    const isSessionValid = await weixinApi.checkSession()
    
    if (!isSessionValid) {
      console.log('会话已过期，需要重新登录')
      return false
    }

    // 尝试刷新用户信息
    const userInfo = await weixinApi.refreshUserInfo()
    
    if (userInfo) {
      console.log('登录状态刷新成功:', userInfo)
      return true
    } else {
      console.log('刷新用户信息失败')
      return false
    }
  } catch (error) {
    console.error('刷新登录状态失败:', error)
    return false
  }
}

/**
 * 清除登录状态
 */
export function clearLoginStatus(): void {
  try {
    weixinApi.clearLocalUserData()
    console.log('登录状态已清除')
  } catch (error) {
    console.error('清除登录状态失败:', error)
  }
}

/**
 * 获取登录状态信息
 * @returns 登录状态详情
 */
export function getLoginStatusInfo(): {
  isLoggedIn: boolean
  userInfo: UserInfo | null
  openid: string | null
} {
  const isLoggedIn = isUserLoggedIn()
  const userInfo = getCurrentUser()
  const openid = weixinApi.getOpenid()

  return {
    isLoggedIn,
    userInfo,
    openid
  }
}

/**
 * 监听登录状态变化（用于页面显示时检查）
 * @param callback 状态变化回调
 */
export function onLoginStatusChange(callback: (isLoggedIn: boolean) => void): void {
  // 这里可以实现登录状态变化的监听逻辑
  // 目前简单返回当前状态
  const isLoggedIn = isUserLoggedIn()
  callback(isLoggedIn)
}

/**
 * 页面级别的登录检查装饰器
 * 用于页面onLoad或onShow时检查登录状态
 */
export function withLoginCheck(
  pageHandler: () => void | Promise<void>,
  options?: {
    showToast?: boolean
    toastMessage?: string
    redirectUrl?: string
    required?: boolean // 是否必须登录
  }
) {
  return async () => {
    const { required = true } = options || {}

    if (required) {
      const isLoggedIn = await checkLoginAndRedirect(options)
      if (!isLoggedIn) {
        return
      }
    }

    try {
      await pageHandler()
    } catch (error) {
      console.error('页面处理失败:', error)
    }
  }
}

/**
 * 检查是否是微信code重复使用错误
 * @param error 错误对象
 * @returns 是否是code重复使用错误
 */
export function isWeixinCodeUsedError(error: any): boolean {
  const errorMessage = error instanceof Error ? error.message : String(error)
  return errorMessage.includes('code已过期') ||
         errorMessage.includes('code已经被使用') ||
         errorMessage.includes('code been used') ||
         errorMessage.includes('40163')
}

/**
 * 处理微信登录重试逻辑
 * @param loginFunction 登录函数
 * @param maxRetries 最大重试次数
 * @returns 登录结果
 */
export async function handleWeixinLoginWithRetry<T>(
  loginFunction: () => Promise<T>,
  maxRetries: number = 2
): Promise<T> {
  let lastError: any

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`微信登录尝试 ${attempt}/${maxRetries}`)
      return await loginFunction()
    } catch (error) {
      lastError = error
      console.error(`微信登录第 ${attempt} 次尝试失败:`, error)

      // 如果是code重复使用错误且还有重试机会，继续重试
      if (isWeixinCodeUsedError(error) && attempt < maxRetries) {
        console.log('检测到code重复使用错误，准备重试...')
        // 等待一小段时间再重试
        await new Promise(resolve => setTimeout(resolve, 1000))
        continue
      }

      // 其他错误或重试次数用完，直接抛出
      throw error
    }
  }

  throw lastError
}
