<template>
  <view class="phone-auth-container">
    <view class="auth-modal" v-if="showModal">
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">完善个人信息</text>
          <text class="modal-subtitle">为了更好的游戏体验，请授权获取您的手机号</text>
        </view>

        <view class="modal-body">
          <view class="user-info" v-if="userProfile">
            <image class="avatar" :src="userProfile.avatarUrl || '/static/default-avatar.png'" mode="aspectFill"></image>
            <text class="nickname">{{ userProfile.nickname || '微信用户' }}</text>
          </view>

          <view class="auth-buttons">
            <!-- 微信小程序手机号授权按钮 -->
            <!-- #ifdef MP-WEIXIN -->
            <button 
              class="auth-btn primary" 
              open-type="getPhoneNumber" 
              @getphonenumber="onGetPhoneNumber"
              :loading="isLoading"
            >
              <text class="btn-text">授权手机号</text>
            </button>
            <!-- #endif -->

            <!-- 非微信小程序环境的模拟按钮 -->
            <!-- #ifndef MP-WEIXIN -->
            <button 
              class="auth-btn primary" 
              @click="onMockPhoneAuth"
              :loading="isLoading"
            >
              <text class="btn-text">模拟授权手机号</text>
            </button>
            <!-- #endif -->

            <button class="auth-btn secondary" @click="onSkip">
              <text class="btn-text">跳过</text>
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { weixinApi } from '../api'
import type { WeixinUserProfile } from '../api/types'

// Props
interface Props {
  openid: string
  visible: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  success: [userInfo: any]
  error: [error: string]
  skip: []
}>()

// 响应式数据
const showModal = ref(false)
const isLoading = ref(false)
const userProfile = ref<WeixinUserProfile | null>(null)

// 监听visible变化
watch(() => props.visible, (newVal) => {
  showModal.value = newVal
  if (newVal) {
    loadUserProfile()
  }
})

onMounted(() => {
  showModal.value = props.visible
  if (props.visible) {
    loadUserProfile()
  }
})

/**
 * 加载用户资料（简化版本，不再调用getUserProfile）
 */
const loadUserProfile = async () => {
  try {
    // 设置默认用户信息
    userProfile.value = {
      openid: '',
      nickname: '微信用户',
      avatarUrl: '',
      gender: 0,
      city: '',
      province: '',
      country: ''
    }
    console.log('使用默认用户信息')
  } catch (error) {
    console.warn('设置默认用户资料失败:', error)
  }
}

/**
 * 处理微信手机号授权
 */
const onGetPhoneNumber = async (e: any) => {
  console.log('手机号授权结果:', e)
  
  if (e.detail.errMsg === 'getPhoneNumber:ok') {
    isLoading.value = true
    
    try {
      // 这里需要将 encryptedData 和 iv 发送到服务端解密
      // 实际项目中需要调用服务端接口解密手机号
      console.log('获取到加密的手机号数据:', e.detail)
      
      // 暂时使用模拟手机号进行绑定
      await performPhoneBind('')
      
    } catch (error) {
      console.error('手机号绑定失败:', error)
      emit('error', '绑定失败，请重试')
    } finally {
      isLoading.value = false
    }
  } else {
    console.log('用户拒绝授权手机号')
    emit('error', '需要授权手机号才能继续使用')
  }
}

/**
 * 模拟手机号授权（非微信环境）
 */
const onMockPhoneAuth = async () => {
  isLoading.value = true
  
  try {
    await performPhoneBind('')
  } catch (error) {
    console.error('模拟绑定失败:', error)
    emit('error', '绑定失败，请重试')
  } finally {
    isLoading.value = false
  }
}

/**
 * 执行手机号绑定
 */
const performPhoneBind = async (phone: string) => {
  try {
    const userInfo = await weixinApi.bindPhone({
      openid: props.openid,
      phone: phone,
      nickname: userProfile.value?.nickname || '微信用户',
      avatarUrl: userProfile.value?.avatarUrl || ''
    })
    
    console.log('手机号绑定成功:', userInfo)
    showModal.value = false
    emit('success', userInfo)
    
  } catch (error) {
    console.error('手机号绑定失败:', error)
    throw error
  }
}

/**
 * 跳过授权
 */
const onSkip = () => {
  showModal.value = false
  emit('skip')
}
</script>

<style scoped>
.phone-auth-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.auth-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.modal-content {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx;
  width: 100%;
  max-width: 600rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.modal-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.modal-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.modal-body {
  text-align: center;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 48rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-bottom: 16rpx;
  border: 4rpx solid #f0f0f0;
}

.nickname {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.auth-buttons {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.auth-btn {
  padding: 24rpx;
  border-radius: 16rpx;
  border: none;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.auth-btn.primary {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(116, 185, 255, 0.3);
}

.auth-btn.primary:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(116, 185, 255, 0.4);
}

.auth-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
}

.auth-btn.secondary:hover {
  background: #e9ecef;
}

.btn-text {
  color: inherit;
  font-size: inherit;
  font-weight: inherit;
}
</style>
