"use strict";
const common_vendor = require("../../common/vendor.js");
const api_weixin = require("../../api/weixin.js");
const utils_audio = require("../../utils/audio.js");
const utils_auth = require("../../utils/auth.js");
if (!Math) {
  PhoneAuth();
}
const PhoneAuth = () => "../../components/PhoneAuth.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const isLoading = common_vendor.ref(false);
    const loadingMessage = common_vendor.ref("");
    const showPhoneAuth = common_vendor.ref(false);
    const pendingOpenid = common_vendor.ref("");
    const isLoginInProgress = common_vendor.ref(false);
    const redirectUrl = common_vendor.ref("/pages/index/index");
    common_vendor.onLoad((options) => {
      console.log("登录页面加载，参数:", options);
      if (options == null ? void 0 : options.redirect) {
        redirectUrl.value = decodeURIComponent(options.redirect);
      }
      checkExistingLogin();
    });
    common_vendor.onMounted(() => {
      utils_audio.audioManager.playBackgroundMusic("menu");
    });
    const checkExistingLogin = () => {
      if (isLoginInProgress.value) {
        return;
      }
      if (utils_auth.isUserLoggedIn()) {
        const userInfo = utils_auth.getCurrentUser();
        console.log("用户已登录:", userInfo);
        common_vendor.index.showToast({
          title: "已登录，正在跳转到首页",
          icon: "success",
          duration: 1500
        });
        setTimeout(() => {
          redirectToTarget();
        }, 1500);
      }
    };
    const handleWeixinLogin = async () => {
      if (isLoading.value || isLoginInProgress.value) {
        console.log("登录正在进行中，忽略重复请求");
        return;
      }
      isLoading.value = true;
      isLoginInProgress.value = true;
      loadingMessage.value = "正在获取微信授权...";
      try {
        utils_audio.audioManager.playSoundEffect("click");
        console.log("开始微信登录流程...");
        api_weixin.weixinApi.clearLocalUserData();
        const loginResponse = await api_weixin.weixinApi.performWeixinLogin();
        console.log("微信登录响应:", loginResponse);
        if (loginResponse.status === "success") {
          loadingMessage.value = "登录成功，正在跳转...";
          common_vendor.index.showToast({
            title: "登录成功，正在跳转到首页",
            icon: "success",
            duration: 1500
          });
          setTimeout(() => {
            redirectToTarget();
          }, 1500);
        } else if (loginResponse.status === "need_bind") {
          loadingMessage.value = "需要绑定手机号";
          pendingOpenid.value = loginResponse.openid;
          showPhoneAuth.value = true;
        } else {
          throw new Error("登录失败：" + loginResponse.message);
        }
      } catch (error) {
        console.error("微信登录失败:", error);
        if (utils_auth.isWeixinCodeUsedError(error)) {
          common_vendor.index.showToast({
            title: "登录状态异常，请稍后重试",
            icon: "none",
            duration: 3e3
          });
        } else {
          const errorMessage = error instanceof Error ? error.message : String(error);
          const is500Error = errorMessage.includes("500") || (error == null ? void 0 : error.statusCode) === 500;
          if (is500Error) {
            common_vendor.index.showModal({
              title: "登录失败",
              content: "服务器暂时不可用，请稍后重试或联系技术支持",
              showCancel: false,
              confirmText: "确定"
            });
          } else {
            common_vendor.index.showToast({
              title: "登录失败，请重试",
              icon: "error",
              duration: 2e3
            });
          }
        }
      } finally {
        isLoading.value = false;
        isLoginInProgress.value = false;
        loadingMessage.value = "";
      }
    };
    const onPhoneAuthSuccess = (userInfo) => {
      console.log("手机号绑定成功:", userInfo);
      showPhoneAuth.value = false;
      common_vendor.index.showToast({
        title: "登录成功，正在跳转到首页",
        icon: "success",
        duration: 1500
      });
      setTimeout(() => {
        redirectToTarget();
      }, 1500);
    };
    const onPhoneAuthError = (error) => {
      console.error("手机号授权失败:", error);
      showPhoneAuth.value = false;
      common_vendor.index.showToast({
        title: error,
        icon: "error",
        duration: 2e3
      });
    };
    const onPhoneAuthSkip = () => {
      console.log("跳过手机号授权");
      showPhoneAuth.value = false;
      common_vendor.index.showToast({
        title: "登录成功，正在跳转到首页",
        icon: "success",
        duration: 1500
      });
      setTimeout(() => {
        redirectToTarget();
      }, 1500);
    };
    const redirectToTarget = () => {
      console.log("登录成功，跳转到首页");
      common_vendor.index.switchTab({
        url: "/pages/index/index",
        success: () => {
          console.log("成功跳转到首页");
        },
        fail: (error) => {
          console.error("跳转首页失败:", error);
          common_vendor.index.reLaunch({
            url: "/pages/index/index"
          });
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(handleWeixinLogin),
        b: isLoading.value,
        c: isLoading.value,
        d: isLoading.value
      }, isLoading.value ? {
        e: common_vendor.t(loadingMessage.value)
      } : {}, {
        f: showPhoneAuth.value
      }, showPhoneAuth.value ? {
        g: common_vendor.o(onPhoneAuthSuccess),
        h: common_vendor.o(onPhoneAuthError),
        i: common_vendor.o(onPhoneAuthSkip),
        j: common_vendor.p({
          visible: showPhoneAuth.value,
          openid: pendingOpenid.value
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-45258083"]]);
wx.createPage(MiniProgramPage);
