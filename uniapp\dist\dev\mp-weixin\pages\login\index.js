"use strict";
const common_vendor = require("../../common/vendor.js");
const api_weixin = require("../../api/weixin.js");
const utils_audio = require("../../utils/audio.js");
const utils_auth = require("../../utils/auth.js");
if (!Math) {
  PhoneAuth();
}
const PhoneAuth = () => "../../components/PhoneAuth.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const isLoading = common_vendor.ref(false);
    const loadingMessage = common_vendor.ref("");
    const showPhoneAuth = common_vendor.ref(false);
    const pendingOpenid = common_vendor.ref("");
    const redirectUrl = common_vendor.ref("/pages/index/index");
    common_vendor.onLoad((options) => {
      console.log("登录页面加载，参数:", options);
      if (options == null ? void 0 : options.redirect) {
        redirectUrl.value = decodeURIComponent(options.redirect);
      }
      checkExistingLogin();
    });
    common_vendor.onMounted(() => {
      utils_audio.audioManager.playBackgroundMusic("menu");
    });
    const checkExistingLogin = () => {
      if (utils_auth.isUserLoggedIn()) {
        const userInfo = utils_auth.getCurrentUser();
        console.log("用户已登录:", userInfo);
        common_vendor.index.showToast({
          title: "已登录，正在跳转...",
          icon: "success",
          duration: 1500
        });
        setTimeout(() => {
          redirectToTarget();
        }, 1500);
      }
    };
    const handleWeixinLogin = async () => {
      if (isLoading.value)
        return;
      isLoading.value = true;
      loadingMessage.value = "正在获取微信授权...";
      try {
        utils_audio.audioManager.playSoundEffect("click");
        console.log("开始微信登录流程...");
        const loginResponse = await api_weixin.weixinApi.performWeixinLogin();
        console.log("微信登录响应:", loginResponse);
        if (loginResponse.status === "success") {
          loadingMessage.value = "登录成功！";
          common_vendor.index.showToast({
            title: "登录成功",
            icon: "success",
            duration: 2e3
          });
          setTimeout(() => {
            redirectToTarget();
          }, 2e3);
        } else if (loginResponse.status === "need_bind") {
          loadingMessage.value = "需要绑定手机号";
          pendingOpenid.value = loginResponse.openid;
          showPhoneAuth.value = true;
        } else {
          throw new Error("登录失败：" + loginResponse.message);
        }
      } catch (error) {
        console.error("微信登录失败:", error);
        common_vendor.index.showToast({
          title: "登录失败，请重试",
          icon: "error",
          duration: 2e3
        });
      } finally {
        isLoading.value = false;
        loadingMessage.value = "";
      }
    };
    const onPhoneAuthSuccess = (userInfo) => {
      console.log("手机号绑定成功:", userInfo);
      showPhoneAuth.value = false;
      common_vendor.index.showToast({
        title: "登录成功",
        icon: "success",
        duration: 2e3
      });
      setTimeout(() => {
        redirectToTarget();
      }, 2e3);
    };
    const onPhoneAuthError = (error) => {
      console.error("手机号授权失败:", error);
      showPhoneAuth.value = false;
      common_vendor.index.showToast({
        title: error,
        icon: "error",
        duration: 2e3
      });
    };
    const onPhoneAuthSkip = () => {
      console.log("跳过手机号授权");
      showPhoneAuth.value = false;
      common_vendor.index.showToast({
        title: "登录成功",
        icon: "success",
        duration: 2e3
      });
      setTimeout(() => {
        redirectToTarget();
      }, 2e3);
    };
    const redirectToTarget = () => {
      console.log("跳转到目标页面:", redirectUrl.value);
      if (redirectUrl.value === "/pages/index/index") {
        common_vendor.index.switchTab({
          url: "/pages/index/index"
        });
      } else {
        common_vendor.index.navigateTo({
          url: redirectUrl.value,
          fail: () => {
            common_vendor.index.switchTab({
              url: "/pages/index/index"
            });
          }
        });
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(handleWeixinLogin),
        b: isLoading.value,
        c: isLoading.value,
        d: isLoading.value
      }, isLoading.value ? {
        e: common_vendor.t(loadingMessage.value)
      } : {}, {
        f: showPhoneAuth.value
      }, showPhoneAuth.value ? {
        g: common_vendor.o(onPhoneAuthSuccess),
        h: common_vendor.o(onPhoneAuthError),
        i: common_vendor.o(onPhoneAuthSkip),
        j: common_vendor.p({
          visible: showPhoneAuth.value,
          openid: pendingOpenid.value
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-45258083"]]);
wx.createPage(MiniProgramPage);
