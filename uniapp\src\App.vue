<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
import { ref, reactive } from 'vue'
import weixinApi from './api/weixin'
import type { UserInfo, UserState } from './api/types'

// 用户状态管理
const userState = reactive<UserState>({
  userInfo: null,
  isLoggedIn: false,
  isLoading: false,
  error: null
})

// 应用启动时的初始化
onLaunch(async () => {
  console.log("App Launch");
  await initializeApp()
});

onShow(() => {
  console.log("App Show");
  // 应用从后台进入前台时，刷新用户信息
  refreshUserInfo()
});

onHide(() => {
  console.log("App Hide");
});

/**
 * 应用初始化
 */
const initializeApp = async () => {
  try {
    userState.isLoading = true
    userState.error = null

    // 1. 检查本地是否有用户信息
    const localUserInfo = weixinApi.getLocalUserInfo()
    const currentOpenid = weixinApi.getOpenid()

    if (localUserInfo && currentOpenid && currentOpenid !== 'openid') {
      // 有本地用户信息，设置登录状态
      userState.userInfo = localUserInfo
      userState.isLoggedIn = true
      console.log('从本地存储恢复用户信息:', localUserInfo)
      console.log('当前使用的 openid:', currentOpenid)

      // 尝试刷新用户信息（静默更新）
      try {
        const freshUserInfo = await weixinApi.getUserInfo()
        userState.userInfo = freshUserInfo
        console.log('用户信息已更新:', freshUserInfo)
      } catch (error) {
        console.warn('刷新用户信息失败，使用本地缓存:', error)
      }
    } else {
      // 没有本地用户信息，需要进行登录流程
      console.log('未找到本地用户信息，需要登录')
      console.log('当前 openid:', currentOpenid)
      await handleUserLogin()
    }
  } catch (error) {
    console.error('应用初始化失败:', error)
    userState.error = '应用初始化失败'
  } finally {
    userState.isLoading = false
  }
}

/**
 * 处理用户登录流程
 */
const handleUserLogin = async () => {
  try {
    // #ifdef MP-WEIXIN
    // 微信小程序环境：进行微信登录
    await performWeixinLogin()
    // #endif

    // #ifndef MP-WEIXIN
    // 非微信小程序环境：使用模拟登录（开发测试用）
    await performMockLogin()
    // #endif
  } catch (error) {
    console.error('用户登录失败:', error)
    userState.error = '登录失败，请重试'
  }
}

/**
 * 执行微信登录（按照uniapp官方文档和WEIXIN_LOGIN_FLOW.md实现）
 * 只使用uni.login获取code，简化登录流程
 */
const performWeixinLogin = async () => {
  try {
    // 1. 检查登录状态是否过期
    const isSessionValid = await weixinApi.checkSession()
    console.log('登录状态检查:', isSessionValid ? '有效' : '已过期')

    // 2. 如果登录状态过期或首次登录，重新登录
    if (!isSessionValid || !userState.userInfo) {
      console.log('开始微信登录流程...')

      // 3. 执行完整的微信登录流程（只使用uni.login获取code）
      const loginResponse = await weixinApi.performWeixinLogin({
        phone: '', // 实际项目中需要用户授权获取
        nickname: '微信用户', // 可选参数
        avatarUrl: '' // 可选参数
      })

      // 4. 处理登录结果
      if (loginResponse.status === 'success') {
        // 登录成功
        userState.userInfo = loginResponse.userInfo
        userState.isLoggedIn = true
        console.log('微信登录成功:', loginResponse.userInfo)

        // 获取关卡信息
        await loadUserlevel()

        // 显示登录成功提示
        uni.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 2000
        })
      } else if (loginResponse.status === 'need_bind') {
        // 需要绑定手机号
        console.log('需要绑定手机号:', loginResponse.openid)
        await handlePhoneBinding(loginResponse.openid)
      }
    } else {
      // 登录状态有效，直接获取关卡信息
      console.log('登录状态有效，直接获取关卡信息')
      await loadUserlevel()
    }
  } catch (error) {
    console.error('微信登录流程失败:', error)
    throw error
  }
}

/**
 * 处理手机号绑定
 */
const handlePhoneBinding = async (openid: string) => {
  try {
    // 实际项目中这里应该显示绑定页面让用户输入手机号
    // 这里为了演示，使用默认手机号进行绑定
    const userInfo = await weixinApi.bindPhone({
      openid: openid,
      phone: '', // 实际项目中需要用户输入或授权获取
      nickname: '微信用户', // 默认昵称
      avatarUrl: '' // 默认头像
    })

    userState.userInfo = userInfo
    userState.isLoggedIn = true

    console.log('手机号绑定成功:', userInfo)

    // 获取关卡信息
    await loadUserlevel()

    // 显示绑定成功提示
    uni.showToast({
      title: '注册成功',
      icon: 'success',
      duration: 2000
    })
  } catch (error) {
    console.error('手机号绑定失败:', error)
    throw error
  }
}

/**
 * 执行模拟登录（开发测试用）
 */
const performMockLogin = async () => {
  try {
    console.log('执行模拟登录')

    // 在非微信环境中，直接使用微信登录流程（会返回模拟数据）
    await performWeixinLogin()
  } catch (error) {
    console.error('模拟登录失败:', error)
    throw error
  }
}



/**
 * 刷新用户信息
 */
const refreshUserInfo = async () => {
  try {
    const freshUserInfo = await weixinApi.refreshUserInfo()
    if (freshUserInfo) {
      userState.userInfo = freshUserInfo
      console.log('用户信息已刷新:', freshUserInfo)
    }
  } catch (error) {
    console.warn('刷新用户信息失败:', error)
  }
}

/**
 * 加载用户关卡信息
 */
const loadUserlevel = async () => {
  try {
    console.log('正在加载用户关卡信息...')

    // 获取关卡列表
    const level = await weixinApi.getLevels()
    console.log('用户关卡信息加载成功:', level)

    // 将关卡信息保存到全局，供其他页面使用
    const app = getApp()
    if (app) {
      app.globalData = {
        ...app.globalData,
        userlevel: level
      }
    }

    // 也可以保存到本地存储
    uni.setStorageSync('userlevel', JSON.stringify(level))

    return level
  } catch (error) {
    console.error('加载用户关卡信息失败:', error)
    // 不抛出错误，避免影响登录流程
  }
}

/**
 * 用户登出
 */
const logout = () => {
  weixinApi.clearLocalUserData()
  userState.userInfo = null
  userState.isLoggedIn = false
  userState.error = null

  console.log('用户已登出')

  uni.showToast({
    title: '已退出登录',
    icon: 'success',
    duration: 1500
  })
}

// 将用户状态和方法挂载到全局，供其他页面使用
const app = getApp()
if (app) {
  app.globalData = {
    ...app.globalData,
    userState,
    refreshUserInfo,
    logout
  }
}
</script>
<style></style>
