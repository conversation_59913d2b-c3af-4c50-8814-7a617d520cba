
.member-center-container.data-v-a17a8d98 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 40rpx;
}

/* 顶部导航 */
.nav-header.data-v-a17a8d98 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 60rpx 24rpx 24rpx;
  position: relative;
}
.nav-back.data-v-a17a8d98 {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}
.nav-back.data-v-a17a8d98:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.3);
}
.nav-back-icon.data-v-a17a8d98 {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
}
.nav-title.data-v-a17a8d98 {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.nav-placeholder.data-v-a17a8d98 {
  width: 60rpx;
}

/* 用户信息卡片 */
.user-card.data-v-a17a8d98 {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 0 24rpx 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}
.user-avatar.data-v-a17a8d98 {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.avatar-text.data-v-a17a8d98 {
  font-size: 40rpx;
  font-weight: bold;
  color: #ffffff;
}
.user-info.data-v-a17a8d98 {
  flex: 1;
}
.user-name.data-v-a17a8d98 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 8rpx;
}
.user-status.data-v-a17a8d98 {
  display: flex;
  align-items: center;
}
.vip-badge.data-v-a17a8d98 {
  background: linear-gradient(135deg, #ffd700, #ffb347);
  color: #8b4513;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
}
.normal-badge.data-v-a17a8d98 {
  background: #f0f0f0;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

/* VIP状态卡片 */
.vip-status-card.data-v-a17a8d98 {
  background: linear-gradient(135deg, #ffd700, #ffb347);
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 0 24rpx 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.3);
}
.vip-header.data-v-a17a8d98 {
  text-align: center;
  margin-bottom: 24rpx;
}
.vip-title.data-v-a17a8d98 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #8b4513;
  margin-bottom: 8rpx;
}
.vip-subtitle.data-v-a17a8d98 {
  display: block;
  font-size: 24rpx;
  color: #8b4513;
  opacity: 0.8;
}

/* 升级VIP卡片 */
.upgrade-vip-card.data-v-a17a8d98 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 0 24rpx 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}
.upgrade-header.data-v-a17a8d98 {
  text-align: center;
  margin-bottom: 24rpx;
}
.upgrade-title.data-v-a17a8d98 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 8rpx;
}
.upgrade-subtitle.data-v-a17a8d98 {
  display: block;
  font-size: 24rpx;
  color: #718096;
}
.upgrade-benefits.data-v-a17a8d98,
.vip-benefits.data-v-a17a8d98 {
  margin-bottom: 24rpx;
}
.benefit-item.data-v-a17a8d98 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.benefit-icon.data-v-a17a8d98 {
  font-size: 24rpx;
  margin-right: 12rpx;
  width: 32rpx;
}
.benefit-text.data-v-a17a8d98 {
  font-size: 24rpx;
  color: #2d3748;
  flex: 1;
}
.vip-status-card .benefit-text.data-v-a17a8d98 {
  color: #8b4513;
}
.upgrade-btn.data-v-a17a8d98 {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
  transition: all 0.2s ease;
}
.upgrade-btn.data-v-a17a8d98:active {
  transform: scale(0.98);
  opacity: 0.9;
}
.upgrade-btn-text.data-v-a17a8d98 {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffffff;
}

/* 游戏统计卡片 */
.stats-card.data-v-a17a8d98 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 0 24rpx 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}
.stats-header.data-v-a17a8d98 {
  margin-bottom: 24rpx;
}
.stats-title.data-v-a17a8d98 {
  font-size: 28rpx;
  font-weight: bold;
  color: #2d3748;
}
.stats-grid.data-v-a17a8d98 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}
.stat-item.data-v-a17a8d98 {
  text-align: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}
.stat-value.data-v-a17a8d98 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8rpx;
}
.stat-label.data-v-a17a8d98 {
  display: block;
  font-size: 22rpx;
  color: #718096;
}

/* 功能菜单 */
.menu-section.data-v-a17a8d98 {
  margin: 0 24rpx;
}
.menu-item.data-v-a17a8d98 {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  cursor: pointer;
}
.menu-item.data-v-a17a8d98:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.8);
}
.menu-icon.data-v-a17a8d98 {
  font-size: 28rpx;
  margin-right: 16rpx;
  width: 40rpx;
}
.menu-text.data-v-a17a8d98 {
  flex: 1;
  font-size: 26rpx;
  color: #2d3748;
  font-weight: 500;
}
.menu-arrow.data-v-a17a8d98 {
  font-size: 24rpx;
  color: #718096;
  font-weight: bold;
}
