"use strict";
require("../api/request.js");
function analyzeError(error) {
  if (!error) {
    return {
      type: "unknown",
      description: "未知错误",
      suggestions: ["请重试"]
    };
  }
  const errorMessage = error.message || String(error);
  const statusCode = error.statusCode;
  if (statusCode === 500) {
    return {
      type: "server_error",
      description: "服务器内部错误",
      suggestions: [
        "检查服务器是否正常运行",
        "检查数据库连接",
        "检查微信API配置",
        "查看服务器日志"
      ]
    };
  }
  if (statusCode === 400) {
    return {
      type: "bad_request",
      description: "请求参数错误",
      suggestions: [
        "检查微信code是否有效",
        "检查请求参数格式",
        "确认code未被重复使用"
      ]
    };
  }
  if (errorMessage.includes("network") || errorMessage.includes("timeout")) {
    return {
      type: "network_error",
      description: "网络连接错误",
      suggestions: [
        "检查网络连接",
        "检查服务器地址配置",
        "重试请求"
      ]
    };
  }
  if (errorMessage.includes("code")) {
    return {
      type: "code_error",
      description: "微信code相关错误",
      suggestions: [
        "重新获取微信code",
        "检查code是否过期",
        "确认code格式正确"
      ]
    };
  }
  return {
    type: "unknown",
    description: errorMessage,
    suggestions: ["请联系技术支持"]
  };
}
exports.analyzeError = analyzeError;
