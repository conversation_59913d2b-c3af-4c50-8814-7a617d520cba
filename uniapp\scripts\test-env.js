#!/usr/bin/env node

/**
 * 环境配置测试脚本
 * 用于验证不同环境下的配置是否正确
 */

const fs = require('fs');
const path = require('path');

// 测试环境配置
function testEnvironmentConfig() {
  const nodeEnv = process.env.NODE_ENV || 'development';
  console.log(`🧪 测试环境配置 - 当前环境: ${nodeEnv}`);
  
  // 检查环境变量文件
  const envFile = `.env.${nodeEnv}`;
  const envPath = path.join(__dirname, '..', envFile);
  
  console.log(`📁 检查环境文件: ${envFile}`);
  
  if (!fs.existsSync(envPath)) {
    console.error(`❌ 环境文件不存在: ${envFile}`);
    process.exit(1);
  }
  
  // 读取环境文件内容
  const envContent = fs.readFileSync(envPath, 'utf8');
  console.log(`📄 环境文件内容:`);
  console.log(envContent);
  
  // 解析环境变量
  const envVars = {};
  envContent.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, value] = trimmed.split('=');
      if (key && value) {
        envVars[key] = value;
      }
    }
  });
  
  console.log(`🔧 解析的环境变量:`);
  console.log(envVars);
  
  // 验证必要的配置
  const requiredVars = ['NODE_ENV', 'VITE_API_BASE_URL'];
  const missingVars = requiredVars.filter(varName => !envVars[varName]);
  
  if (missingVars.length > 0) {
    console.error(`❌ 缺少必要的环境变量: ${missingVars.join(', ')}`);
    process.exit(1);
  }
  
  console.log(`✅ 环境配置验证通过`);
  
  // 显示配置摘要
  console.log(`📊 配置摘要:`);
  console.log(`  环境: ${envVars.NODE_ENV}`);
  console.log(`  API地址: ${envVars.VITE_API_BASE_URL}`);
  console.log(`  调试模式: ${envVars.VITE_DEBUG}`);
}

// 运行测试
try {
  testEnvironmentConfig();
} catch (error) {
  console.error('❌ 环境配置测试失败:', error.message);
  process.exit(1);
}
