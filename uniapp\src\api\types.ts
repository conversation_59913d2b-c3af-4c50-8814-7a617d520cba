/**
 * API相关的TypeScript类型定义
 * 根据WEIXIN_API文档定义的接口类型
 */

// 基础响应类型
export interface ApiResponse<T = any> {
  data?: T
  message?: string
  statusCode?: number
  error?: string
}

// 用户绑定请求参数
export interface UserBindRequest {
  openid: string          // 微信用户openid（必填）
  phone: string           // 用户手机号（必填）
  nickname?: string       // 用户昵称（可选）
  avatarUrl?: string      // 头像URL（可选）
}

// 用户信息响应数据
export interface UserInfo {
  id: string                    // 8位数字用户ID
  maskedPhone: string           // 加密手机号
  nickname?: string             // 用户昵称
  avatarUrl?: string            // 头像URL
  unlockedLevels: number        // 已解锁关卡数
  completedLevelIds: string[]   // 已完成关卡ID列表
  totalGames: number            // 总游戏次数
  totalCompletions: number      // 总通关次数
  lastPlayTime: string          // 最后游戏时间
  createdAt: string             // 创建时间

  // 每日解锁限制相关字段
  isVip: boolean                // VIP状态
  dailyUnlockLimit: number      // 每日解锁限制（默认15）
  dailyUnlockCount: number      // 当日解锁次数
  dailyShared: boolean          // 当日是否已分享
  lastPlayDate: string          // 最后游戏日期（YYYY-MM-DD）
  totalShares: number           // 总分享次数
}

// 关卡信息
export interface LevelInfo {
  id: string              // 关卡UUID
  name: string            // 关卡名称
  difficulty: number      // 难度等级
  description: string     // 关卡描述
  isUnlocked: boolean     // 是否已解锁
  isCompleted: boolean    // 是否已完成
  createdAt: string       // 创建时间
}

// 词汇信息
export interface WordInfo {
  english: string         // 英文单词
  chinese: string         // 中文翻译
  phonetic: string        // 音标
}

// 词库信息
export interface LibraryInfo {
  id: number              // 词库ID
  name: string            // 词库名称
  words: WordInfo[]       // 词汇列表
}

// 本地关卡数据（兼容现有代码）
export interface LocalLevel {
  id: number
  levelNumber: string
  name: string
  description: string
  library: LibraryInfo
  locked: boolean
  completed: boolean
}

// 请求配置
export interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  data?: any
  header?: Record<string, string>
  timeout?: number
  retry?: number
}

// 请求响应
export interface RequestResponse<T = any> {
  data: T
  statusCode: number
  header: Record<string, string>
  cookies?: string[]
}

// 错误信息
export interface ApiError {
  message: string
  statusCode?: number
  error?: string
  details?: any
}

// 加载状态
export interface LoadingState {
  isLoading: boolean
  error: string | null
}

// 用户状态
export interface UserState extends LoadingState {
  userInfo: UserInfo | null
  isLoggedIn: boolean
}

// 关卡状态
export interface levelState extends LoadingState {
  level: LevelInfo[]
  currentLevel: LevelInfo | null
}

// 游戏统计
export interface GameStats {
  totalGames: number
  totalCompletions: number
  completionRate: number
  unlockedLevels: number
  completedlevel: number
}

// 微信小程序用户信息（微信API返回）
export interface WeixinUserProfile {
  openid: string
  nickname?: string
  avatarUrl?: string
  gender?: number
  city?: string
  province?: string
  country?: string
}

// 微信登录结果
export interface WeixinLoginResult {
  code: string
  openid?: string
  sessionKey?: string
}

// 微信登录请求参数
export interface WeixinLoginRequest {
  code: string
  phone?: string
  nickname?: string
  avatarUrl?: string
}

// 微信登录响应 - 成功
export interface WeixinLoginSuccessResponse {
  status: 'success'
  message: string
  openid: string
  userInfo: UserInfo
}

// 微信登录响应 - 需要绑定
export interface WeixinLoginNeedBindResponse {
  status: 'need_bind'
  message: string
  openid: string
}

// 微信登录响应联合类型
export type WeixinLoginResponse = WeixinLoginSuccessResponse | WeixinLoginNeedBindResponse

// 手机号绑定请求参数
export interface BindPhoneRequest {
  openid: string
  phone: string
  nickname?: string
  avatarUrl?: string
}

// 微信配置检查响应
export interface WeixinConfigResponse {
  appId: string
  appSecret: string
  isConfigured: boolean
}

// 分享配置响应
export interface ShareConfigResponse {
  title: string
  path: string
  imageUrl: string
  desc?: string
  summary?: string
  query?: string
}

// 分享配置请求参数
export interface ShareConfigRequest {
  page?: string
  levelId?: string
  userId?: string
}

// 分享奖励请求参数
export interface ShareRewardRequest {
  userId: string
  shareType: 'app_message' | 'timeline' | 'system'
  page?: string
  levelId?: string
  timestamp?: number
}

// 分享奖励响应
export interface ShareRewardResponse {
  success: boolean
  message: string
  reward: {
    type: 'extra_chance' | 'coins' | 'experience'
    amount: number
    description: string
  }
  userInfo?: UserInfo
}

// 每日状态响应
export interface DailyStatusResponse {
  id: string                    // 用户ID
  dailyUnlockCount: number      // 当日解锁次数
  dailyUnlockLimit: number      // 每日解锁限制
  remainingUnlocks: number      // 剩余解锁次数
  dailyShared: boolean          // 当日是否已分享
  isVip: boolean                // VIP状态
  lastPlayDate: string          // 最后游戏日期
  totalShares: number           // 总分享次数
  canUnlock: boolean            // 是否可以解锁
}

// 分享接口请求参数（更新）
export interface ShareRequest {
  openid: string                // 微信openid
}

// 分享接口响应
export interface ShareResponse {
  status: 'success' | 'already_shared' | 'error'
  message: string
  userId?: string
  dailyUnlockCount?: number
  dailyUnlockLimit?: number
  remainingUnlocks?: number
  isVip?: boolean
  totalShares?: number
}

// 手机号获取结果
export interface PhoneNumberResult {
  phoneNumber: string
  purePhoneNumber: string
  countryCode: string
}

// 关卡详情（包含词组信息）
export interface LevelDetail {
  id: string
  name: string
  difficulty: number
  description: string
  phrases: PhraseInfo[]
  isUnlocked: boolean
  isCompleted: boolean
  createdAt: string
}

// 词组信息
export interface PhraseInfo {
  id: string
  text: string
  meaning: string
  createdAt: string
}

// 完成关卡请求参数
export interface CompleteLevelRequest {
  levelId: string
  openid?: string
}

// 完成关卡响应
export interface CompleteLevelResponse {
  message: string
  userId: string
  levelId: string
  unlockedLevels: number
  totalCompletions: number
  hasUnlockedNewLevel: boolean
  dailyUnlockCount: number
  dailyUnlockLimit: number
  remainingUnlocks: number
  isVip: boolean
}

// 游戏开始请求参数
export interface StartGameRequest {
  levelId: string
}

// 用户统计信息
export interface UserStats {
  totalGames: number
  totalCompletions: number
  completionRate: number
  unlockedLevels: number
  completedlevel: number
  lastPlayTime: string
}

// VIP套餐信息
export interface VipPackage {
  id: string                    // 套餐ID
  name: string                  // 套餐名称
  description: string           // 套餐描述
  price: number                 // 价格（分）
  duration: number              // 时长（天）
  isActive: boolean             // 是否启用
}

// 创建支付订单请求参数
export interface CreatePaymentRequest {
  openid: string                // 微信openid
  packageId: string             // VIP套餐ID
}

// 微信支付参数
export interface WeixinPaymentParams {
  appId: string                 // 小程序AppID
  timeStamp: string             // 时间戳
  nonceStr: string              // 随机字符串
  package: string               // 预支付交易会话标识
  signType: string              // 签名类型
  paySign: string               // 签名
}

// 支付订单信息
export interface PaymentOrder {
  id: string                    // 订单ID
  out_trade_no: string          // 商户订单号
  transaction_id?: string       // 微信支付订单号
  description: string           // 商品描述
  total: number                 // 订单金额（分）
  status: 'PENDING' | 'SUCCESS' | 'FAILED' | 'CANCELLED' | 'REFUNDED'  // 订单状态
  vip_package_id: string        // VIP套餐ID
  paid_at?: string              // 支付时间
  created_at: string            // 创建时间
}

// 支付状态查询响应
export interface PaymentStatusResponse extends PaymentOrder {
  // 继承PaymentOrder的所有字段
}

// 支付订单列表响应
export type PaymentOrdersResponse = PaymentOrder[]

// 游戏设置
export interface GameSettings {
  backgroundMusic: boolean      // 背景音乐开关
  soundEffects: boolean         // 音效开关
  vibration: boolean            // 震动开关
}

// 设置项
export interface SettingItem {
  key: keyof GameSettings
  label: string
  description: string
  type: 'switch' | 'slider' | 'select'
  value: boolean | number | string
}

// 全局配置
export interface GlobalConfig {
  // 背景音乐配置
  backgroundMusic: {
    mainPageUrl: string         // 首页背景音乐URL
    gamePageUrl: string         // 游戏页面背景音乐URL
    menuPageUrl: string         // 菜单页面背景音乐URL
  }
  // 帮助页面配置
  helpPage: {
    url: string                 // 帮助页面URL
    title: string               // 帮助页面标题
  }
  // 其他全局配置
  app: {
    name: string                // 应用名称
    version: string             // 应用版本
    description: string         // 应用描述
  }
}

// 全局配置响应
export interface GlobalConfigResponse {
  success: boolean
  data: GlobalConfig
  message?: string
  timestamp: string
}
