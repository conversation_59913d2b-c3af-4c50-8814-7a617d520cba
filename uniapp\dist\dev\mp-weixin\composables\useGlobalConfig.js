"use strict";
const common_vendor = require("../common/vendor.js");
const api_weixin = require("../api/weixin.js");
const globalConfig = common_vendor.ref(null);
const isLoading = common_vendor.ref(false);
const error = common_vendor.ref(null);
const defaultConfig = {
  backgroundMusic: {
    mainPageUrl: "/static/audio/bg-main.mp3",
    gamePageUrl: "/static/audio/bg-game.mp3",
    menuPageUrl: "/static/audio/bg-menu.mp3"
  },
  helpPage: {
    url: "/pages/help/index",
    title: "游戏帮助"
  },
  app: {
    name: "英语单词游戏",
    version: "1.0.0",
    description: "挑战你的词汇量，提升英语水平！"
  }
};
function useGlobalConfig() {
  const fetchGlobalConfig = async (forceRefresh = false) => {
    if (globalConfig.value && !forceRefresh) {
      return globalConfig.value;
    }
    isLoading.value = true;
    error.value = null;
    try {
      const config = await api_weixin.weixinApi.getGlobalConfig();
      globalConfig.value = config;
      console.log("全局配置加载成功:", config);
      return config;
    } catch (err) {
      const errorMessage = err.message || "获取全局配置失败";
      error.value = errorMessage;
      console.error("获取全局配置失败:", err);
      globalConfig.value = defaultConfig;
      return defaultConfig;
    } finally {
      isLoading.value = false;
    }
  };
  const resetConfig = () => {
    globalConfig.value = null;
    error.value = null;
    isLoading.value = false;
  };
  const getBackgroundMusicConfig = common_vendor.computed(() => {
    var _a;
    return ((_a = globalConfig.value) == null ? void 0 : _a.backgroundMusic) || defaultConfig.backgroundMusic;
  });
  const getHelpPageConfig = common_vendor.computed(() => {
    var _a;
    return ((_a = globalConfig.value) == null ? void 0 : _a.helpPage) || defaultConfig.helpPage;
  });
  const getAppConfig = common_vendor.computed(() => {
    var _a;
    return ((_a = globalConfig.value) == null ? void 0 : _a.app) || defaultConfig.app;
  });
  const getBackgroundMusicUrl = (pageType) => {
    const musicConfig = getBackgroundMusicConfig.value;
    switch (pageType) {
      case "main":
        return musicConfig.mainPageUrl;
      case "game":
        return musicConfig.gamePageUrl;
      case "menu":
        return musicConfig.menuPageUrl;
      default:
        return musicConfig.mainPageUrl;
    }
  };
  const navigateToHelp = () => {
    const helpConfig = getHelpPageConfig.value;
    if (helpConfig.url.startsWith("http://") || helpConfig.url.startsWith("https://")) {
      common_vendor.index.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent(helpConfig.url)}&title=${encodeURIComponent(helpConfig.title)}`
      });
    } else {
      common_vendor.index.navigateTo({
        url: helpConfig.url
      });
    }
  };
  const showHelpModal = () => {
    getHelpPageConfig.value;
    common_vendor.index.navigateTo({ url: "/pages/help/index" });
  };
  const initializeGlobalConfig = async () => {
    console.log("初始化全局配置...");
    await fetchGlobalConfig();
  };
  return {
    // 状态
    globalConfig: common_vendor.computed(() => globalConfig.value),
    isLoading: common_vendor.computed(() => isLoading.value),
    error: common_vendor.computed(() => error.value),
    // 计算属性
    backgroundMusicConfig: getBackgroundMusicConfig,
    helpPageConfig: getHelpPageConfig,
    appConfig: getAppConfig,
    // 方法
    fetchGlobalConfig,
    resetConfig,
    getBackgroundMusicUrl,
    navigateToHelp,
    showHelpModal,
    initializeGlobalConfig
  };
}
useGlobalConfig();
exports.useGlobalConfig = useGlobalConfig;
