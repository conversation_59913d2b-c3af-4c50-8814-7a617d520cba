
.login-container.data-v-45258083 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}
.background-decoration.data-v-45258083 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}
.decoration-circle.data-v-45258083 {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float-45258083 6s ease-in-out infinite;
}
.circle-1.data-v-45258083 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
  animation-delay: 0s;
}
.circle-2.data-v-45258083 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: -30rpx;
  animation-delay: 2s;
}
.circle-3.data-v-45258083 {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  left: 50%;
  animation-delay: 4s;
}
@keyframes float-45258083 {
0%, 100% { transform: translateY(0px) rotate(0deg);
}
50% { transform: translateY(-20px) rotate(180deg);
}
}
.login-content.data-v-45258083 {
  position: relative;
  z-index: 2;
  padding: 100rpx 60rpx 60rpx;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.header-section.data-v-45258083 {
  text-align: center;
  margin-bottom: 100rpx;
}
.logo-container.data-v-45258083 {
  margin-bottom: 40rpx;
}
.logo-icon.data-v-45258083 {
  font-size: 120rpx;
  display: block;
}
.app-title.data-v-45258083 {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  display: block;
  margin-bottom: 20rpx;
}
.app-subtitle.data-v-45258083 {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}
.login-form.data-v-45258083 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
}
.form-header.data-v-45258083 {
  text-align: center;
  margin-bottom: 60rpx;
}
.form-title.data-v-45258083 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}
.form-subtitle.data-v-45258083 {
  font-size: 26rpx;
  color: #666;
  display: block;
}
.login-buttons.data-v-45258083 {
  margin-bottom: 60rpx;
}
.login-btn.data-v-45258083 {
  width: 100%;
  height: 100rpx;
  border-radius: 50rpx;
  border: none;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}
.login-btn.data-v-45258083:last-child {
  margin-bottom: 0;
}
.weixin-btn.data-v-45258083 {
  background: linear-gradient(135deg, #07c160 0%, #00a854 100%);
  color: white;
  box-shadow: 0 8rpx 25rpx rgba(7, 193, 96, 0.3);
}
.weixin-btn.data-v-45258083:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(7, 193, 96, 0.3);
}
.guest-btn.data-v-45258083 {
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  color: #666;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}
.guest-btn.data-v-45258083:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}
.btn-icon.data-v-45258083 {
  font-size: 36rpx;
  margin-right: 20rpx;
}
.btn-text.data-v-45258083 {
  font-size: 32rpx;
}
.login-tips.data-v-45258083 {
  padding-top: 40rpx;
  border-top: 1rpx solid #eee;
}
.tip-text.data-v-45258083 {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 15rpx;
  line-height: 1.5;
}
.tip-text.data-v-45258083:last-child {
  margin-bottom: 0;
}
.loading-overlay.data-v-45258083 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.loading-content.data-v-45258083 {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  min-width: 300rpx;
}
.loading-spinner.data-v-45258083 {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin-45258083 1s linear infinite;
  margin: 0 auto 30rpx;
}
@keyframes spin-45258083 {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.loading-text.data-v-45258083 {
  font-size: 28rpx;
  color: #333;
  display: block;
}
