/**
 * 全局配置管理 Composable
 * 用于管理应用的全局配置，包括背景音乐、帮助页面等
 */

import { ref, computed } from 'vue'
import type { GlobalConfig } from '@/api/types'
import { weixinApi } from '@/api/weixin'

// 全局配置状态
const globalConfig = ref<GlobalConfig | null>(null)
const isLoading = ref(false)
const error = ref<string | null>(null)

// 默认配置
const defaultConfig: GlobalConfig = {
  backgroundMusic: {
    mainPageUrl: '/static/audio/bg-main.mp3',
    gamePageUrl: '/static/audio/bg-game.mp3',
    menuPageUrl: '/static/audio/bg-menu.mp3'
  },
  helpPage: {
    url: '/pages/help/index',
    title: '游戏帮助'
  },
  app: {
    name: '英语单词游戏',
    version: '1.0.0',
    description: '挑战你的词汇量，提升英语水平！'
  }
}

export function useGlobalConfig() {
  /**
   * 获取全局配置
   */
  const fetchGlobalConfig = async (forceRefresh = false) => {
    // 如果已有配置且不强制刷新，直接返回
    if (globalConfig.value && !forceRefresh) {
      return globalConfig.value
    }

    isLoading.value = true
    error.value = null

    try {
      const config = await weixinApi.getGlobalConfig()
      globalConfig.value = config
      console.log('全局配置加载成功:', config)
      return config
    } catch (err) {
      const errorMessage = (err as Error).message || '获取全局配置失败'
      error.value = errorMessage
      console.error('获取全局配置失败:', err)
      
      // 使用默认配置
      globalConfig.value = defaultConfig
      return defaultConfig
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 重置配置状态
   */
  const resetConfig = () => {
    globalConfig.value = null
    error.value = null
    isLoading.value = false
  }

  /**
   * 获取背景音乐配置
   */
  const getBackgroundMusicConfig = computed(() => {
    return globalConfig.value?.backgroundMusic || defaultConfig.backgroundMusic
  })

  /**
   * 获取帮助页面配置
   */
  const getHelpPageConfig = computed(() => {
    return globalConfig.value?.helpPage || defaultConfig.helpPage
  })

  /**
   * 获取应用配置
   */
  const getAppConfig = computed(() => {
    return globalConfig.value?.app || defaultConfig.app
  })

  /**
   * 根据页面类型获取背景音乐URL
   */
  const getBackgroundMusicUrl = (pageType: 'main' | 'game' | 'menu') => {
    const musicConfig = getBackgroundMusicConfig.value
    switch (pageType) {
      case 'main':
        return musicConfig.mainPageUrl
      case 'game':
        return musicConfig.gamePageUrl
      case 'menu':
        return musicConfig.menuPageUrl
      default:
        return musicConfig.mainPageUrl
    }
  }

  /**
   * 跳转到帮助页面
   */
  const navigateToHelp = () => {
    const helpConfig = getHelpPageConfig.value
    
    // 检查是否是外部链接
    if (helpConfig.url.startsWith('http://') || helpConfig.url.startsWith('https://')) {
      // 外部链接，使用 web-view 打开
      uni.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent(helpConfig.url)}&title=${encodeURIComponent(helpConfig.title)}`
      })
    } else {
      // 内部页面，直接跳转
      uni.navigateTo({
        url: helpConfig.url
      })
    }
  }

  /**
   * 显示帮助信息（弹窗形式）
   */
  const showHelpModal = () => {
    const helpConfig = getHelpPageConfig.value
    
    // uni.showModal({
    //   title: helpConfig.title,
    //   content: '🎮 游戏玩法：\n\n1. 选择关卡开始游戏\n2. 点击两张相同的卡片进行配对\n3. 完成所有配对即可通关\n4. 通关后解锁下一关\n\n💡 小贴士：\n• VIP用户可无限解锁关卡\n• 分享游戏可获得额外解锁机会\n• 点击右上角设置可调整音效',
    //   showCancel: true,
    //   cancelText: '关闭',
    //   confirmText: '查看详情',
    //   success: (res) => {
    //     if (res.confirm) {
    //       // 用户点击查看详情，跳转到帮助页面
    //       navigateToHelp()
    //     }
    //   }
    // })
    uni.navigateTo({ url: '/pages/help/index' })
  }

  /**
   * 初始化全局配置（应用启动时调用）
   */
  const initializeGlobalConfig = async () => {
    console.log('初始化全局配置...')
    await fetchGlobalConfig()
  }

  return {
    // 状态
    globalConfig: computed(() => globalConfig.value),
    isLoading: computed(() => isLoading.value),
    error: computed(() => error.value),
    
    // 计算属性
    backgroundMusicConfig: getBackgroundMusicConfig,
    helpPageConfig: getHelpPageConfig,
    appConfig: getAppConfig,
    
    // 方法
    fetchGlobalConfig,
    resetConfig,
    getBackgroundMusicUrl,
    navigateToHelp,
    showHelpModal,
    initializeGlobalConfig
  }
}

// 创建全局实例
export const globalConfigManager = useGlobalConfig()

// 自动初始化（可选）
export const initGlobalConfig = () => {
  return globalConfigManager.initializeGlobalConfig()
}
