<template>
  <view class="member-center-container">
    <!-- 顶部导航 -->
    <view class="nav-header">
    </view>

    <!-- 用户信息卡片 -->
    <view v-if="userInfo" class="user-card">
      <view class="user-avatar">
        <text class="avatar-text">{{ getUserInitial() }}</text>
      </view>
      <view class="user-info">
        <text class="user-name">{{ userInfo.nickname || userInfo.maskedPhone || '游戏玩家' }}</text>
        <view class="user-status">
          <text v-if="dailyStatus?.isVip" class="vip-badge">👑 VIP会员</text>
          <text v-else class="normal-badge">普通用户</text>
        </view>
      </view>
    </view>

    <!-- VIP状态卡片 -->
    <view v-if="dailyStatus?.isVip" class="vip-status-card">
      <view class="vip-header">
        <text class="vip-title">👑 VIP特权</text>
        <text class="vip-subtitle">尊享无限制游戏体验</text>
      </view>
      <view class="vip-benefits">
        <view class="benefit-item">
          <text class="benefit-icon">🎮</text>
          <text class="benefit-text">无限解锁所有关卡</text>
        </view>
        <view class="benefit-item">
          <text class="benefit-icon">⚡</text>
          <text class="benefit-text">无限制游戏次数</text>
        </view>
        <view class="benefit-item">
          <text class="benefit-icon">🎯</text>
          <text class="benefit-text">专属VIP标识</text>
        </view>
      </view>
    </view>

    <!-- 升级VIP卡片 -->
    <view v-else class="upgrade-vip-card">
      <view class="upgrade-header">
        <text class="upgrade-title">🎯 升级VIP会员</text>
        <text class="upgrade-subtitle">解锁全部功能，畅享无限游戏</text>
      </view>
      <view class="upgrade-benefits">
        <view class="benefit-item">
          <text class="benefit-icon">🎮</text>
          <text class="benefit-text">无限解锁所有关卡</text>
        </view>
        <view class="benefit-item">
          <text class="benefit-icon">⚡</text>
          <text class="benefit-text">无限制游戏次数</text>
        </view>
        <view class="benefit-item">
          <text class="benefit-icon">🎯</text>
          <text class="benefit-text">专属VIP标识</text>
        </view>
        <view class="benefit-item">
          <text class="benefit-icon">🏆</text>
          <text class="benefit-text">优先客服支持</text>
        </view>
      </view>
      <button class="upgrade-btn" @click="showVipPackages">
        <text class="upgrade-btn-text">立即升级VIP</text>
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import weixinApi from '../../api/weixin'
import { audioManager } from '../../utils/audio'
import { checkLoginAndRedirect } from '../../utils/auth'
import type { UserInfo, DailyStatusResponse, VipPackage } from '../../api/types'

// 用户信息
const userInfo = ref<UserInfo | null>(null)

// 每日状态信息
const dailyStatus = ref<DailyStatusResponse | null>(null)

// VIP套餐信息
const vipPackages = ref<VipPackage[]>([])
const isLoadingVipPackages = ref(false)

/**
 * 页面加载
 */
onLoad(async () => {
  // 检查登录状态
  const isLoggedIn = await checkLoginAndRedirect({
    toastMessage: '请先登录以访问会员中心',
    redirectUrl: '/pages/login/index?redirect=' + encodeURIComponent('/pages/member-center/index')
  })

  if (!isLoggedIn) {
    return
  }

  loadPageData()
})

/**
 * 加载页面数据
 */
const loadPageData = async () => {
  try {
    // 加载用户信息
    await loadUserInfo()
    
    // 加载每日状态
    await loadDailyStatus()
  } catch (error) {
    console.error('加载页面数据失败:', error)
  }
}

/**
 * 加载用户信息
 */
const loadUserInfo = async () => {
  try {
    const freshUserInfo = await weixinApi.refreshUserInfo()
    if (freshUserInfo) {
      userInfo.value = freshUserInfo
    } else {
      const localUserInfo = weixinApi.getLocalUserInfo()
      userInfo.value = localUserInfo
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
    const localUserInfo = weixinApi.getLocalUserInfo()
    userInfo.value = localUserInfo
  }
}

/**
 * 加载每日状态
 */
const loadDailyStatus = async () => {
  try {
    const status = await weixinApi.getDailyStatus()
    dailyStatus.value = status
  } catch (error) {
    console.error('加载每日状态失败:', error)
  }
}

/**
 * 获取用户名首字母
 */
const getUserInitial = () => {
  const name = userInfo.value?.nickname || userInfo.value?.maskedPhone || '游戏玩家'
  return name.charAt(0).toUpperCase()
}

/**
 * 返回上一页
 */
const goBack = () => {
  audioManager.playSoundEffect('click')
  uni.navigateBack()
}

/**
 * 显示VIP套餐
 */
const showVipPackages = async () => {
  try {
    audioManager.playSoundEffect('click')
    
    isLoadingVipPackages.value = true
    const packages = await weixinApi.getVipPackages()
    vipPackages.value = packages

    const packageOptions = packages.map(pkg =>
      `${pkg.name} - ¥${(pkg.price / 100).toFixed(2)}`
    )

    uni.showActionSheet({
      itemList: packageOptions,
      success: (res) => {
        const selectedPackage = packages[res.tapIndex]
        if (selectedPackage) {
          showPaymentConfirm(selectedPackage)
        }
      }
    })
  } catch (error) {
    console.error('加载VIP套餐失败:', error)
    uni.showToast({
      title: '加载套餐失败',
      icon: 'none'
    })
  } finally {
    isLoadingVipPackages.value = false
  }
}

/**
 * 显示支付确认
 */
const showPaymentConfirm = (vipPackage: VipPackage) => {
  const price = (vipPackage.price / 100).toFixed(2)

  uni.showModal({
    title: '确认购买',
    content: `${vipPackage.name}\n价格：¥${price}\n时长：${vipPackage.duration}天\n\n${vipPackage.description}`,
    showCancel: true,
    cancelText: '取消',
    confirmText: '立即支付',
    success: (res) => {
      if (res.confirm) {
        handleVipPayment(vipPackage)
      }
    }
  })
}

/**
 * 处理VIP支付
 */
const handleVipPayment = async (vipPackage: VipPackage) => {
  try {
    uni.showLoading({ title: '正在创建订单...' })

    const paymentSuccess = await weixinApi.requestPayment(vipPackage.id)

    if (paymentSuccess) {
      await loadUserInfo()
      await loadDailyStatus()

      uni.showModal({
        title: '支付成功',
        content: `恭喜您成为VIP会员！\n已获得${vipPackage.duration}天VIP特权`,
        showCancel: false,
        confirmText: '太棒了'
      })
    }
  } catch (error) {
    console.error('VIP支付失败:', error)
    uni.showModal({
      title: '支付失败',
      content: '支付过程中出现问题，请稍后重试',
      showCancel: false,
      confirmText: '知道了'
    })
  } finally {
    uni.hideLoading()
  }
}

/**
 * 跳转到设置页面
 */
const goToSettings = () => {
  audioManager.playSoundEffect('click')
  // 这里可以打开设置弹窗或跳转到设置页面
  uni.showToast({
    title: '设置功能开发中',
    icon: 'none'
  })
}

/**
 * 显示帮助
 */
const showHelp = () => {
  audioManager.playSoundEffect('click')
  uni.showModal({
    title: '帮助中心',
    content: '游戏玩法：\n1. 点击开始游戏选择关卡\n2. 找到英文和中文的配对\n3. 完成所有配对即可过关\n\n如有其他问题，请联系客服',
    showCancel: false,
    confirmText: '知道了'
  })
}

/**
 * 联系客服
 */
const contactSupport = () => {
  audioManager.playSoundEffect('click')
  uni.showModal({
    title: '联系客服',
    content: '客服微信：game_support\n客服QQ：123456789\n工作时间：9:00-18:00',
    showCancel: false,
    confirmText: '知道了'
  })
}
</script>

<style scoped>
.member-center-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 40rpx;
}

/* 顶部导航 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 60rpx 24rpx 24rpx;
  position: relative;
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-back:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.3);
}

.nav-back-icon {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.nav-placeholder {
  width: 60rpx;
}

/* 用户信息卡片 */
.user-card {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 0 24rpx 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.avatar-text {
  font-size: 40rpx;
  font-weight: bold;
  color: #ffffff;
}

.user-info {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 8rpx;
}

.user-status {
  display: flex;
  align-items: center;
}

.vip-badge {
  background: linear-gradient(135deg, #ffd700, #ffb347);
  color: #8b4513;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.normal-badge {
  background: #f0f0f0;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

/* VIP状态卡片 */
.vip-status-card {
  background: linear-gradient(135deg, #ffd700, #ffb347);
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 0 24rpx 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.3);
}

.vip-header {
  text-align: center;
  margin-bottom: 24rpx;
}

.vip-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #8b4513;
  margin-bottom: 8rpx;
}

.vip-subtitle {
  display: block;
  font-size: 24rpx;
  color: #8b4513;
  opacity: 0.8;
}

/* 升级VIP卡片 */
.upgrade-vip-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 0 24rpx 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.upgrade-header {
  text-align: center;
  margin-bottom: 24rpx;
}

.upgrade-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 8rpx;
}

.upgrade-subtitle {
  display: block;
  font-size: 24rpx;
  color: #718096;
}

.upgrade-benefits,
.vip-benefits {
  margin-bottom: 24rpx;
}

.benefit-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.benefit-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
  width: 32rpx;
}

.benefit-text {
  font-size: 24rpx;
  color: #2d3748;
  flex: 1;
}

.vip-status-card .benefit-text {
  color: #8b4513;
}

.upgrade-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
  transition: all 0.2s ease;
}

.upgrade-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.upgrade-btn-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffffff;
}

/* 游戏统计卡片 */
.stats-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 0 24rpx 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.stats-header {
  margin-bottom: 24rpx;
}

.stats-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2d3748;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 22rpx;
  color: #718096;
}

/* 功能菜单 */
.menu-section {
  margin: 0 24rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  cursor: pointer;
}

.menu-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.8);
}

.menu-icon {
  font-size: 28rpx;
  margin-right: 16rpx;
  width: 40rpx;
}

.menu-text {
  flex: 1;
  font-size: 26rpx;
  color: #2d3748;
  font-weight: 500;
}

.menu-arrow {
  font-size: 24rpx;
  color: #718096;
  font-weight: bold;
}
</style>
