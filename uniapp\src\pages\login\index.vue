<template>
  <view class="login-container">
    <!-- 背景装饰 -->
    <view class="background-decoration">
      <view class="decoration-circle circle-1"></view>
      <view class="decoration-circle circle-2"></view>
      <view class="decoration-circle circle-3"></view>
    </view>

    <!-- 主要内容 -->
    <view class="login-content">
      <!-- Logo和标题 -->
      <view class="header-section">
        <view class="logo-container">
          <text class="logo-icon">🎮</text>
        </view>
        <text class="app-title">趣护消消乐</text>
        <text class="app-subtitle">挑战你的词汇量，提升英语水平</text>
      </view>

      <!-- 登录表单 -->
      <view class="login-form">
        <view class="form-header">
          <text class="form-title">欢迎回来</text>
          <text class="form-subtitle">请登录以继续您的学习之旅</text>
        </view>

        <!-- 登录按钮区域 -->
        <view class="login-buttons">
          <!-- 微信登录按钮 -->
          <!-- #ifdef MP-WEIXIN -->
          <button 
            class="login-btn weixin-btn" 
            @click="handleWeixinLogin"
            :loading="isLoading"
            :disabled="isLoading"
          >
            <text class="btn-icon">🔐</text>
            <text class="btn-text">微信一键登录</text>
          </button>
          <!-- #endif -->

          <!-- 非微信环境的模拟登录 -->
          <!-- #ifndef MP-WEIXIN -->
          <button 
            class="login-btn weixin-btn" 
            @click="handleMockLogin"
            :loading="isLoading"
            :disabled="isLoading"
          >
            <text class="btn-icon">🔐</text>
            <text class="btn-text">模拟登录（开发测试）</text>
          </button>
          <!-- #endif -->
        </view>

        <!-- 登录说明 -->
        <view class="login-tips">
          <text class="tip-text">• 登录后可以保存游戏进度</text>
          <text class="tip-text">• 解锁更多关卡和功能</text>
          <text class="tip-text">• 查看学习统计和成就</text>
        </view>
      </view>
    </view>

    <!-- 加载提示 -->
    <view v-if="isLoading" class="loading-overlay">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">{{ loadingMessage }}</text>
      </view>
    </view>

    <!-- 手机号授权组件 -->
    <PhoneAuth 
      v-if="showPhoneAuth"
      :visible="showPhoneAuth"
      :openid="pendingOpenid"
      @success="onPhoneAuthSuccess"
      @error="onPhoneAuthError"
      @skip="onPhoneAuthSkip"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import weixinApi from '@/api/weixin'
import { audioManager } from '@/utils/audio'
import { isUserLoggedIn, getCurrentUser, handleWeixinLoginWithRetry, isWeixinCodeUsedError } from '@/utils/auth'
import PhoneAuth from '@/components/PhoneAuth.vue'
import type { UserInfo, WeixinLoginResponse } from '@/api/types'

// 响应式数据
const isLoading = ref(false)
const loadingMessage = ref('')
const showPhoneAuth = ref(false)
const pendingOpenid = ref('')

// 防重复请求标志
const isLoginInProgress = ref(false)

// 页面参数
const redirectUrl = ref('/pages/index/index') // 默认跳转到首页

/**
 * 页面加载
 */
onLoad((options) => {
  console.log('登录页面加载，参数:', options)
  
  // 获取跳转参数
  if (options?.redirect) {
    redirectUrl.value = decodeURIComponent(options.redirect)
  }
  
  // 检查是否已经登录
  checkExistingLogin()
})

onMounted(() => {
  // 播放背景音乐
  audioManager.playBackgroundMusic('menu')
})

/**
 * 检查现有登录状态
 */
const checkExistingLogin = () => {
  // 如果正在登录中，不重复检查
  if (isLoginInProgress.value) {
    return
  }

  if (isUserLoggedIn()) {
    const userInfo = getCurrentUser()
    console.log('用户已登录:', userInfo)

    uni.showToast({
      title: '已登录，正在跳转...',
      icon: 'success',
      duration: 1500
    })

    setTimeout(() => {
      redirectToTarget()
    }, 1500)
  }
}

/**
 * 处理微信登录
 */
const handleWeixinLogin = async () => {
  // 防止重复请求
  if (isLoading.value || isLoginInProgress.value) {
    console.log('登录正在进行中，忽略重复请求')
    return
  }

  isLoading.value = true
  isLoginInProgress.value = true
  loadingMessage.value = '正在获取微信授权...'

  try {
    // 播放点击音效
    audioManager.playSoundEffect('click')

    console.log('开始微信登录流程...')

    // 先清除可能存在的旧登录状态
    weixinApi.clearLocalUserData()

    // 使用重试逻辑执行微信登录
    const loginResponse = await handleWeixinLoginWithRetry(async () => {
      return await weixinApi.performWeixinLogin()
    }, 3) // 最多重试3次

    console.log('微信登录响应:', loginResponse)
    
    if (loginResponse.status === 'success') {
      // 登录成功
      loadingMessage.value = '登录成功！'
      
      uni.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 2000
      })
      
      setTimeout(() => {
        redirectToTarget()
      }, 2000)
      
    } else if (loginResponse.status === 'need_bind') {
      // 需要绑定手机号
      loadingMessage.value = '需要绑定手机号'
      pendingOpenid.value = loginResponse.openid
      showPhoneAuth.value = true
      
    } else {
      throw new Error('登录失败：' + (loginResponse as any).message)
    }
    
  } catch (error) {
    console.error('微信登录失败:', error)

    // 检查是否是code重复使用的错误（重试后仍然失败）
    if (isWeixinCodeUsedError(error)) {
      uni.showToast({
        title: '登录状态异常，请稍后重试',
        icon: 'none',
        duration: 3000
      })
    } else {
      uni.showToast({
        title: '登录失败，请重试',
        icon: 'error',
        duration: 2000
      })
    }
  } finally {
    isLoading.value = false
    isLoginInProgress.value = false
    loadingMessage.value = ''
  }
}

/**
 * 处理模拟登录（开发环境）
 */
const handleMockLogin = async () => {
  // 防止重复请求
  if (isLoading.value || isLoginInProgress.value) {
    console.log('登录正在进行中，忽略重复请求')
    return
  }

  isLoading.value = true
  isLoginInProgress.value = true
  loadingMessage.value = '模拟登录中...'
  
  try {
    // 播放点击音效
    audioManager.playSoundEffect('click')
    
    // 模拟登录延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 创建模拟用户信息
    const mockOpenid = 'mock_openid_' + Date.now()
    const mockUserInfo: UserInfo = {
      id: 'mock_user_' + Date.now(),
      maskedPhone: '138****8000',
      nickname: '测试用户',
      avatarUrl: '',
      unlockedLevels: 5,
      completedLevelIds: [],
      totalGames: 0,
      totalCompletions: 0,
      lastPlayTime: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      isVip: false,
      dailyUnlockLimit: 15,
      dailyUnlockCount: 0,
      dailyShared: false,
      lastPlayDate: new Date().toISOString().split('T')[0],
      totalShares: 0
    }

    // 保存到本地存储
    uni.setStorageSync('userInfo', JSON.stringify(mockUserInfo))
    weixinApi.setOpenid(mockOpenid)
    
    loadingMessage.value = '登录成功！'
    
    uni.showToast({
      title: '模拟登录成功',
      icon: 'success',
      duration: 2000
    })
    
    setTimeout(() => {
      redirectToTarget()
    }, 2000)
    
  } catch (error) {
    console.error('模拟登录失败:', error)
    
    uni.showToast({
      title: '登录失败，请重试',
      icon: 'error',
      duration: 2000
    })
  } finally {
    isLoading.value = false
    isLoginInProgress.value = false
    loadingMessage.value = ''
  }
}



/**
 * 手机号授权成功
 */
const onPhoneAuthSuccess = (userInfo: UserInfo) => {
  console.log('手机号绑定成功:', userInfo)
  showPhoneAuth.value = false
  
  uni.showToast({
    title: '登录成功',
    icon: 'success',
    duration: 2000
  })
  
  setTimeout(() => {
    redirectToTarget()
  }, 2000)
}

/**
 * 手机号授权失败
 */
const onPhoneAuthError = (error: string) => {
  console.error('手机号授权失败:', error)
  showPhoneAuth.value = false
  
  uni.showToast({
    title: error,
    icon: 'error',
    duration: 2000
  })
}

/**
 * 跳过手机号授权
 */
const onPhoneAuthSkip = () => {
  console.log('跳过手机号授权')
  showPhoneAuth.value = false
  
  // 跳过授权也算登录成功（只是没有手机号）
  uni.showToast({
    title: '登录成功',
    icon: 'success',
    duration: 2000
  })
  
  setTimeout(() => {
    redirectToTarget()
  }, 2000)
}

/**
 * 跳转到目标页面
 */
const redirectToTarget = () => {
  console.log('跳转到目标页面:', redirectUrl.value)
  
  if (redirectUrl.value === '/pages/index/index') {
    // 跳转到首页，使用switchTab
    uni.switchTab({
      url: '/pages/index/index'
    })
  } else {
    // 跳转到其他页面
    uni.navigateTo({
      url: redirectUrl.value,
      fail: () => {
        // 如果跳转失败，回到首页
        uni.switchTab({
          url: '/pages/index/index'
        })
      }
    })
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: -30rpx;
  animation-delay: 2s;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  left: 50%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.login-content {
  position: relative;
  z-index: 2;
  padding: 100rpx 60rpx 60rpx;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.header-section {
  text-align: center;
  margin-bottom: 100rpx;
}

.logo-container {
  margin-bottom: 40rpx;
}

.logo-icon {
  font-size: 120rpx;
  display: block;
}

.app-title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  display: block;
  margin-bottom: 20rpx;
}

.app-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}

.login-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
}

.form-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.form-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.form-subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.login-buttons {
  margin-bottom: 60rpx;
}

.login-btn {
  width: 100%;
  height: 100rpx;
  border-radius: 50rpx;
  border: none;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.login-btn:last-child {
  margin-bottom: 0;
}

.weixin-btn {
  background: linear-gradient(135deg, #07c160 0%, #00a854 100%);
  color: white;
  box-shadow: 0 8rpx 25rpx rgba(7, 193, 96, 0.3);
}

.weixin-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(7, 193, 96, 0.3);
}

.guest-btn {
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  color: #666;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}

.guest-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.btn-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.btn-text {
  font-size: 32rpx;
}

.login-tips {
  padding-top: 40rpx;
  border-top: 1rpx solid #eee;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 15rpx;
  line-height: 1.5;
}

.tip-text:last-child {
  margin-bottom: 0;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  min-width: 300rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #333;
  display: block;
}
</style>
