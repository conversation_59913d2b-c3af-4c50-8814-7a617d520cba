"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const api_weixin = require("./api/weixin.js");
const utils_env = require("./utils/env.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/game/index.js";
  "./pages/debug/index.js";
  "./pages/webview/index.js";
  "./pages/test-audio/index.js";
  "./pages/level-selection/index.js";
  "./pages/member-center/index.js";
  "./pages/help/index.js";
  "./pages/login/index.js";
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "App",
  setup(__props) {
    const userState = common_vendor.reactive({
      userInfo: null,
      isLoggedIn: false,
      isLoading: false,
      error: null
    });
    common_vendor.onLaunch(async () => {
      console.log("App Launch");
      await initializeApp();
    });
    common_vendor.onShow(() => {
      console.log("App Show");
      refreshUserInfo();
    });
    common_vendor.onHide(() => {
      console.log("App Hide");
    });
    const initializeApp = async () => {
      try {
        userState.isLoading = true;
        userState.error = null;
        const localUserInfo = api_weixin.weixinApi.getLocalUserInfo();
        const currentOpenid = api_weixin.weixinApi.getOpenid();
        if (localUserInfo && currentOpenid && currentOpenid !== "openid") {
          userState.userInfo = localUserInfo;
          userState.isLoggedIn = true;
          console.log("从本地存储恢复用户信息:", localUserInfo);
          console.log("当前使用的 openid:", currentOpenid);
          try {
            const freshUserInfo = await api_weixin.weixinApi.getUserInfo();
            userState.userInfo = freshUserInfo;
            console.log("用户信息已更新:", freshUserInfo);
          } catch (error) {
            console.warn("刷新用户信息失败，使用本地缓存:", error);
          }
        } else {
          console.log("未找到本地用户信息，用户需要手动登录");
          console.log("当前 openid:", currentOpenid);
          userState.isLoggedIn = false;
          if (currentOpenid && currentOpenid !== "openid") {
            console.log("清除旧的openid，避免登录冲突");
            api_weixin.weixinApi.clearLocalUserData();
          }
        }
      } catch (error) {
        console.error("应用初始化失败:", error);
        userState.error = "应用初始化失败";
      } finally {
        userState.isLoading = false;
      }
    };
    const refreshUserInfo = async () => {
      try {
        const freshUserInfo = await api_weixin.weixinApi.refreshUserInfo();
        if (freshUserInfo) {
          userState.userInfo = freshUserInfo;
          console.log("用户信息已刷新:", freshUserInfo);
        }
      } catch (error) {
        console.warn("刷新用户信息失败:", error);
      }
    };
    const logout = () => {
      api_weixin.weixinApi.clearLocalUserData();
      userState.userInfo = null;
      userState.isLoggedIn = false;
      userState.error = null;
      console.log("用户已登出");
      common_vendor.index.showToast({
        title: "已退出登录",
        icon: "success",
        duration: 1500
      });
    };
    const app = getApp();
    if (app) {
      app.globalData = {
        ...app.globalData,
        userState,
        refreshUserInfo,
        logout
      };
    }
    return () => {
    };
  }
});
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  utils_env.logEnvironmentInfo();
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
