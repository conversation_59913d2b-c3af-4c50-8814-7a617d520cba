"use strict";
const common_vendor = require("../common/vendor.js");
require("../api/request.js");
const api_weixin = require("../api/weixin.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "PhoneAuth",
  props: {
    openid: {},
    visible: { type: Boolean }
  },
  emits: ["success", "error", "skip"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const showModal = common_vendor.ref(false);
    const isLoading = common_vendor.ref(false);
    const userProfile = common_vendor.ref(null);
    common_vendor.watch(() => props.visible, (newVal) => {
      showModal.value = newVal;
      if (newVal) {
        loadUserProfile();
      }
    });
    common_vendor.onMounted(() => {
      showModal.value = props.visible;
      if (props.visible) {
        loadUserProfile();
      }
    });
    const loadUserProfile = async () => {
      try {
        userProfile.value = {
          openid: "",
          nickname: "微信用户",
          avatarUrl: "",
          gender: 0,
          city: "",
          province: "",
          country: ""
        };
        console.log("使用默认用户信息");
      } catch (error) {
        console.warn("设置默认用户资料失败:", error);
      }
    };
    const onGetPhoneNumber = async (e) => {
      console.log("手机号授权结果:", e);
      if (e.detail.errMsg === "getPhoneNumber:ok") {
        isLoading.value = true;
        try {
          console.log("获取到加密的手机号数据:", e.detail);
          await performPhoneBind("");
        } catch (error) {
          console.error("手机号绑定失败:", error);
          emit("error", "绑定失败，请重试");
        } finally {
          isLoading.value = false;
        }
      } else {
        console.log("用户拒绝授权手机号");
        emit("error", "需要授权手机号才能继续使用");
      }
    };
    const performPhoneBind = async (phone) => {
      var _a, _b;
      try {
        const userInfo = await api_weixin.weixinApi.bindPhone({
          openid: props.openid,
          phone,
          nickname: ((_a = userProfile.value) == null ? void 0 : _a.nickname) || "微信用户",
          avatarUrl: ((_b = userProfile.value) == null ? void 0 : _b.avatarUrl) || ""
        });
        console.log("手机号绑定成功:", userInfo);
        showModal.value = false;
        emit("success", userInfo);
      } catch (error) {
        console.error("手机号绑定失败:", error);
        throw error;
      }
    };
    const onSkip = () => {
      showModal.value = false;
      emit("skip");
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: showModal.value
      }, showModal.value ? common_vendor.e({
        b: userProfile.value
      }, userProfile.value ? {
        c: userProfile.value.avatarUrl || "/static/default-avatar.png",
        d: common_vendor.t(userProfile.value.nickname || "微信用户")
      } : {}, {
        e: common_vendor.o(onGetPhoneNumber),
        f: isLoading.value,
        g: common_vendor.o(onSkip)
      }) : {});
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-70b951f4"]]);
wx.createComponent(Component);
