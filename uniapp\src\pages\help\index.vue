<template>

      <web-view
        :src="helpUrl"
      ></web-view>

</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useGlobalConfig } from '../../composables/useGlobalConfig'

const helpUrl = ref('')
const isLoading = ref(true)

// 全局配置
const {
  globalConfig,
  helpPageConfig,
  fetchGlobalConfig
} = useGlobalConfig()

/**
 * 页面加载时初始化
 */
onMounted(async () => {
  await loadHelpContent()
})

/**
 * 加载帮助内容
 */
const loadHelpContent = async () => {
  isLoading.value = true

  try {
    // 获取全局配置
    await fetchGlobalConfig()

    const helpConfig = helpPageConfig.value
    helpUrl.value = helpConfig.url
    isLoading.value = false
  } catch (err) {
    console.error('加载帮助内容失败:', err)
    isLoading.value = false
  }
}

/**
 * 返回上一页
 */
const goBack = () => {
  uni.navigateBack({
    delta: 1
  })
}
</script>

<style lang="scss" scoped>
</style>