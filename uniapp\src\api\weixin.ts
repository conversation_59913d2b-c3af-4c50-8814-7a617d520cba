/**
 * 微信小程序API服务类
 * 根据WEIXIN_API文档实现的接口服务
 */

import { getWeixinApiUrl,  STORAGE_KEYS } from './config'
import httpClient from './request'
import { appendQueryToUrl } from './utils'
import type {
  UserBindRequest,
  UserInfo,
  LevelInfo,
  LevelDetail,
  CompleteLevelRequest,
  CompleteLevelResponse,
  WeixinLoginRequest,
  WeixinLoginResponse,
  BindPhoneRequest,
  WeixinConfigResponse,
  ShareConfigResponse,
  ShareConfigRequest,
  ShareRewardRequest,
  ShareRewardResponse,
  DailyStatusResponse,
  ShareRequest,
  ShareResponse,
  VipPackage,
  CreatePaymentRequest,
  WeixinPaymentParams,
  PaymentOrder,
  PaymentStatusResponse,
  PaymentOrdersResponse,
  ApiError,
  WeixinLoginResult,
  GlobalConfig,
  GlobalConfigResponse
} from './types'

// 通用 openid 变量
let openid: string = ''

class WeixinApiService {
  /**
   * 设置 openid
   */
  setOpenid(newOpenid: string): void {
    openid = newOpenid
    // 同时保存到本地存储
    uni.setStorageSync(STORAGE_KEYS.USER_OPENID, newOpenid)
    console.log('设置 openid:', newOpenid)
  }

  /**
   * 获取当前 openid
   */
  getOpenid(): string {
    if (!openid || openid === '') {
      // 如果内存中没有，尝试从本地存储获取
      const localOpenid = uni.getStorageSync(STORAGE_KEYS.USER_OPENID)
      if (localOpenid) {
        openid = localOpenid
      } else {
        // 如果都没有，使用默认值
        openid = ''
      }
    }
    return openid
  }

  /**
   * 初始化 openid（从本地存储恢复）
   */
  initializeOpenid(): void {
    const localOpenid = uni.getStorageSync(STORAGE_KEYS.USER_OPENID)
    if (localOpenid) {
      openid = localOpenid
      console.log('从本地存储恢复 openid:', localOpenid)
    } 
  }
  /**
   * 用户绑定接口
   * POST /api/v1/weixin/user/bind
   */
  async bindUser(params?: Omit<UserBindRequest, 'openid'>): Promise<UserInfo> {
    try {
      const url = getWeixinApiUrl('/user/bind')

      // 使用通用 openid 和传入的参数
      const requestParams: UserBindRequest = {
        openid: this.getOpenid(),
        phone: params?.phone || '', // 默认手机号，实际应该从用户授权获取
        nickname: params?.nickname || '微信用户',
        avatarUrl: params?.avatarUrl || ''
      }

      const response = await httpClient.post<UserInfo>(url, requestParams)

      // 绑定成功后保存用户信息到本地存储
      uni.setStorageSync(STORAGE_KEYS.USER_INFO, JSON.stringify(response))
      this.setOpenid(requestParams.openid) // 确保 openid 已保存

      console.log('用户绑定成功:', response)
      return response
    } catch (error) {
      console.error('用户绑定失败:', error)
      throw error
    }
  }

  /**
   * 获取用户信息（使用通用 openid）
   * GET /api/v1/weixin/user/info?openid={openid}
   */
  async getUserInfo(customOpenid?: string): Promise<UserInfo> {
    try {
      const targetOpenid = customOpenid || this.getOpenid()
      if (!targetOpenid) {
        throw new Error('缺少 openid 参数')
      }
      const url = getWeixinApiUrl(`/user/info?openid=${targetOpenid}`)
      const response = await httpClient.get<UserInfo>(url)

      // 更新本地存储的用户信息
      uni.setStorageSync(STORAGE_KEYS.USER_INFO, JSON.stringify(response))

      console.log('获取用户信息成功:', response)
      return response
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  /**
   * 根据用户ID获取用户信息
   * GET /api/v1/weixin/user/{id}/info
   */
  async getUserInfoById(userId: string): Promise<UserInfo> {
    try {
      const url = getWeixinApiUrl(`/user/${userId}/info`)
      const response = await httpClient.get<UserInfo>(url)
      
      console.log('根据ID获取用户信息成功:', response)
      return response
    } catch (error) {
      console.error('根据ID获取用户信息失败:', error)
      throw error
    }
  }

  /**
   * 获取关卡列表（包含用户进度，使用通用 openid）
   * GET /api/v1/weixin/levels?openid={openid}
   */
  async getLevels(customOpenid?: string): Promise<LevelInfo[]> {
    try {
      const targetOpenid = customOpenid || this.getOpenid()
       if (!targetOpenid) {
        throw new Error('缺少 openid 参数')
      }
      const url = getWeixinApiUrl(`/levels?openid=${targetOpenid}`)
      const response = await httpClient.get<LevelInfo[]>(url)

      console.log('获取关卡列表成功:', response)
      return response
    } catch (error) {
      console.error('获取关卡列表失败:', error)
      throw error
    }
  }

  /**
   * 强制刷新微信登录状态
   * 清除本地缓存并重新获取code
   */
  private async forceRefreshWeixinSession(): Promise<void> {
    return new Promise((resolve) => {
      // #ifdef MP-WEIXIN
      // 先检查并清除可能存在的旧会话
      uni.checkSession({
        success: () => {
          console.log('检测到旧的微信会话，准备清除...')
          // 这里不需要做特殊处理，直接获取新code即可
          resolve()
        },
        fail: () => {
          console.log('没有旧的微信会话')
          resolve()
        }
      })
      // #endif

      // #ifndef MP-WEIXIN
      resolve()
      // #endif
    })
  }

  /**
   * 微信登录获取code（uniapp官方API）
   * 根据官方文档：https://uniapp.dcloud.net.cn/api/plugins/login.html#login
   */
  async weixinLogin(): Promise<WeixinLoginResult> {
    // 先强制刷新会话状态
    await this.forceRefreshWeixinSession()

    return new Promise((resolve, reject) => {
      // #ifdef MP-WEIXIN
      console.log('开始获取新的微信登录code...')
      uni.login({
        provider: 'weixin',
        timeout: 10000, // 设置超时时间
        success: (loginRes) => {
          console.log('微信登录成功:', loginRes)

          // 根据官方文档，success回调包含code字段
          if (loginRes.code) {
            console.log('获取到新的微信code:', loginRes.code)
            resolve({
              code: loginRes.code
            })
          } else {
            reject(new Error('未获取到登录凭证'))
          }
        },
        fail: (error) => {
          console.error('微信登录失败:', error)
          reject(new Error(`微信登录失败: ${error.errMsg || '未知错误'}`))
        }
      })
      // #endif

      // #ifndef MP-WEIXIN
      // 非微信小程序环境，返回模拟数据用于开发测试
      console.log('非微信环境，使用模拟登录数据')
      resolve({
        code: 'mock_code_' + Date.now()
      })
      // #endif
    })
  }

  /**
   * 完整的微信登录流程（按照WEIXIN_LOGIN_FLOW.md实现）
   * 只使用uni.login获取code，其他信息作为可选参数
   * POST /api/v1/weixin/login
   */
  async performWeixinLogin(userInfo?: { phone?: string; nickname?: string; avatarUrl?: string }): Promise<WeixinLoginResponse> {
    try {
      console.log('开始微信登录流程，清除旧的登录状态...')

      // 0. 清除本地登录状态，确保获取新的code
      this.clearLocalUserData()

      // 1. 获取微信登录凭证
      const loginResult = await this.weixinLogin()
      console.log('获取微信登录凭证成功:', loginResult.code)

      // 2. 构造登录请求参数（根据WEIXIN_LOGIN_FLOW.md）
      const loginRequest: WeixinLoginRequest = {
        code: loginResult.code,
        nickname: userInfo?.nickname || '微信用户', // 可选
        avatarUrl: userInfo?.avatarUrl || '' // 可选
      }

    if(userInfo?.phone) {
      loginRequest.phone = userInfo.phone
    }

      console.log('发送登录请求:', loginRequest)

      // 3. 调用服务端登录接口
      const url = getWeixinApiUrl('/login')
      const response = await httpClient.post<WeixinLoginResponse>(url, loginRequest)

      // 4. 处理登录响应
      if (response.status === 'success') {
        // 登录成功，保存用户信息和openid
        uni.setStorageSync(STORAGE_KEYS.USER_INFO, JSON.stringify(response.userInfo))
        this.setOpenid(response.openid)
        console.log('微信登录成功:', response)
      } else if (response.status === 'need_bind') {
        // 需要绑定手机号，保存openid
        this.setOpenid(response.openid)
        console.log('需要绑定手机号:', response)
      }
      return response
    } catch (error) {
      console.error('微信登录流程失败:', error)

      // 检查是否是code重复使用的错误
      const errorMessage = error instanceof Error ? error.message : String(error)
      if (errorMessage.includes('code已经被使用') ||
          errorMessage.includes('code been used') ||
          errorMessage.includes('40163')) {
        console.log('检测到code重复使用错误，清除本地状态...')

        // 清除本地状态
        this.clearLocalUserData()

        // 抛出用户友好的错误信息
        throw new Error('登录状态已过期，请重新尝试')
      }

      throw error
    }
  }

  /**
   * 绑定手机号完成注册
   * POST /api/v1/weixin/bind-phone
   */
  async bindPhone(params: BindPhoneRequest): Promise<UserInfo> {
    try {
      const url = getWeixinApiUrl('/bind-phone')
      const response = await httpClient.post<{ status: string; userInfo: UserInfo; openid: string }>(url, params)

      if (response.status === 'success') {
        // 绑定成功，保存用户信息
        uni.setStorageSync(STORAGE_KEYS.USER_INFO, JSON.stringify(response.userInfo))
        this.setOpenid(response.openid)
        console.log('手机号绑定成功:', response.userInfo)
        return response.userInfo
      } else {
        throw new Error('绑定失败')
      }
    } catch (error) {
      console.error('绑定手机号失败:', error)
      throw error
    }
  }

  /**
   * 检查微信配置状态
   * GET /api/v1/weixin/config
   */
  async checkWeixinConfig(): Promise<WeixinConfigResponse> {
    try {
      const url = getWeixinApiUrl('/config')
      const response = await httpClient.get<WeixinConfigResponse>(url)

      console.log('微信配置状态:', response)
      return response
    } catch (error) {
      console.error('检查微信配置失败:', error)
      throw error
    }
  }

  /**
   * 检查登录状态是否过期（根据官方文档）
   * 仅在微信小程序环境中可用
   */
  async checkSession(): Promise<boolean> {
    return new Promise((resolve) => {
      // #ifdef MP-WEIXIN
      uni.checkSession({
        success: () => {
          console.log('登录状态有效')
          resolve(true)
        },
        fail: () => {
          console.log('登录状态已过期')
          resolve(false)
        }
      })
      // #endif

      // #ifndef MP-WEIXIN
      // 非微信小程序环境，始终返回true
      console.log('非微信环境，跳过登录状态检查')
      resolve(true)
      // #endif
    })
  }

  /**
   * 获取分享配置
   * GET /api/v1/weixin/share-config
   */
  async getShareConfig(params?: ShareConfigRequest): Promise<ShareConfigResponse> {
    try {
      const baseUrl = getWeixinApiUrl('/share-config')

      // 使用工具函数构建查询字符串
      const url = params ? appendQueryToUrl(baseUrl, {
        page: params.page,
        levelId: params.levelId,
        userId: params.userId
      }) : baseUrl

      const response = await httpClient.get<ShareConfigResponse>(url)

      console.log('获取分享配置成功:', response)
      return response
    } catch (error) {
      console.error('获取分享配置失败:', error)

      // 返回默认分享配置
      const defaultConfig: ShareConfigResponse = {
        title: '英语单词游戏 - 挑战你的词汇量！',
        path: '/pages/index/index',
        imageUrl: '/static/share-logo.png',
        desc: '快来挑战英语单词游戏，提升你的词汇量！',
        summary: '和朋友一起学英语，看谁的词汇量更丰富！'
      }

      console.log('使用默认分享配置:', defaultConfig)
      return defaultConfig
    }
  }

  /**
   * 获取每日状态
   * GET /api/v1/weixin/daily-status?openid={openid}
   */
  async getDailyStatus(customOpenid?: string): Promise<DailyStatusResponse> {
    try {
      const targetOpenid = customOpenid || this.getOpenid()
      if (!targetOpenid) {
        throw new Error('缺少 openid 参数')
      }

      const url = getWeixinApiUrl('/daily-status')

      const fullUrl = `${url}?openid=${targetOpenid}`
      const response = await httpClient.get<DailyStatusResponse>(fullUrl)

      console.log('获取每日状态成功:', response)
      return response
    } catch (error) {
      console.error('获取每日状态失败:', error)
      throw error
    }
  }

  /**
   * 用户分享（新版本）
   * POST /api/v1/weixin/share
   */
  async shareForReward(customOpenid?: string): Promise<ShareResponse> {
    try {
      const targetOpenid = customOpenid || this.getOpenid()
       if (!targetOpenid) {
        throw new Error('缺少 openid 参数')
      }

      const url = getWeixinApiUrl('/share')
      const response = await httpClient.post<ShareResponse>(url, {
        openid: targetOpenid
      })

      console.log('分享奖励获取成功:', response)
      return response
    } catch (error) {
      console.error('分享奖励获取失败:', error)
      throw error
    }
  }

  /**
   * 分享后获取奖励（旧版本，保持兼容性）
   * POST /api/v1/weixin/share
   */
  async getShareReward(): Promise<ShareRewardResponse> {
    try {
      const targetOpenid =  this.getOpenid()
       if (!targetOpenid) {
        throw new Error('缺少 openid 参数')
      }

      const url = getWeixinApiUrl('/share')
      const response = await httpClient.post<ShareRewardResponse>(url, {
        openid: targetOpenid,
      })

      console.log('分享奖励获取成功:', response)

      // 如果返回了更新的用户信息，保存到本地
      if (response.userInfo) {
        uni.setStorageSync(STORAGE_KEYS.USER_INFO, JSON.stringify(response.userInfo))
      }

      return response
    } catch (error) {
      console.error('获取分享奖励失败:', error)
      throw error
    }
  }





  /**
   * 检查用户是否已绑定（使用通用 openid）
   */
  async checkUserBound(customOpenid?: string): Promise<boolean> {
    try {
      await this.getUserInfo(customOpenid)
      return true
    } catch (error) {
      // 如果获取用户信息失败（404），说明用户未绑定
      if ((error as ApiError).statusCode === 404) {
        return false
      }
      throw error
    }
  }

  /**
   * 从本地存储获取用户信息
   */
  getLocalUserInfo(): UserInfo | null {
    try {
      const userInfoStr = uni.getStorageSync(STORAGE_KEYS.USER_INFO)
      if (userInfoStr) {
        return JSON.parse(userInfoStr)
      }
      return null
    } catch (error) {
      console.error('获取本地用户信息失败:', error)
      return null
    }
  }

  /**
   * 从本地存储获取openid（兼容旧方法）
   */
  getLocalOpenid(): string | null {
    try {
      return this.getOpenid() || null
    } catch (error) {
      console.error('获取本地openid失败:', error)
      return null
    }
  }

  /**
   * 清除本地用户数据
   */
  clearLocalUserData(): void {
    try {
      uni.removeStorageSync(STORAGE_KEYS.USER_INFO)
      uni.removeStorageSync(STORAGE_KEYS.USER_OPENID)
      // 清除内存中的 openid
      openid = ''
      console.log('清除本地用户数据成功')
    } catch (error) {
      console.error('清除本地用户数据失败:', error)
    }
  }

  /**
   * 刷新用户信息（使用通用 openid）
   */
  async refreshUserInfo(): Promise<UserInfo | null> {
    try {
      return await this.getUserInfo()
    } catch (error) {
      console.error('刷新用户信息失败:', error)
      return null
    }
  }

  /**
   * 获取关卡详情（包含词组信息）
   * GET /api/v1/level/{id}/with-phrases
   */
  async getLevelDetail(levelId: string, customOpenid?:string): Promise<LevelDetail> {
    try {

      const targetOpenid = customOpenid || this.getOpenid()
       if (!targetOpenid) {
        throw new Error('缺少 openid 参数')
      }
      const url = getWeixinApiUrl(`/level/${levelId}?openid=${targetOpenid}`)
      const response = await httpClient.get<LevelDetail>(url)

      console.log('获取关卡详情成功:', response)
      return response
    } catch (error) {
      console.error('获取关卡详情失败:', error)
      throw error
    }
  }

  /**
   * 用户完成关卡
   * POST /api/v1/weixin/level/complete
   */
  async completeLevel(userId: string, levelId: string): Promise<CompleteLevelResponse> {
    try {
      const targetOpenid = this.getOpenid()
       if (!targetOpenid) {
        throw new Error('缺少 openid 参数')
      }

      const url = getWeixinApiUrl(`/level/complete`)
      const requestData: CompleteLevelRequest = {
        levelId: levelId,
        openid: targetOpenid,
      }

      const response = await httpClient.post<CompleteLevelResponse>(url, requestData)

      console.log('关卡完成记录成功:', response)
      return response
    } catch (error) {
      console.error('记录关卡完成失败:', error)
      throw error
    }
  }

  /**
   * 用户完成关卡（旧版本，返回UserInfo）
   * POST /api/v1/weixin/level/complete
   */
  async completeLevelLegacy(userId: string, levelId: string): Promise<UserInfo> {
    try {
      const response = await this.completeLevel(userId, levelId)

      // 从响应中构建UserInfo对象（为了兼容性）
      const userInfo: UserInfo = {
        id: response.userId,
        maskedPhone: '', // 需要从其他地方获取
        unlockedLevels: response.unlockedLevels,
        completedLevelIds: [], // 需要从其他地方获取
        totalGames: 0, // 需要从其他地方获取
        totalCompletions: response.totalCompletions,
        lastPlayTime: new Date().toISOString(),
        createdAt: '', // 需要从其他地方获取
        isVip: response.isVip,
        dailyUnlockLimit: response.dailyUnlockLimit,
        dailyUnlockCount: response.dailyUnlockCount,
        dailyShared: false, // 需要从其他地方获取
        lastPlayDate: new Date().toISOString().split('T')[0],
        totalShares: 0 // 需要从其他地方获取
      }

      // 更新本地存储的用户信息
      uni.setStorageSync(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo))

      return userInfo
    } catch (error) {
      console.error('记录关卡完成失败:', error)
      throw error
    }
  }

  /**
   * 获取VIP套餐列表
   * GET /api/v1/weixin/vip-packages
   */
  async getVipPackages(): Promise<VipPackage[]> {
    try {
      const url = getWeixinApiUrl('/vip-packages')
      const response = await httpClient.get<VipPackage[]>(url)

      console.log('获取VIP套餐列表成功:', response)
      return response
    } catch (error) {
      console.error('获取VIP套餐列表失败:', error)
      throw error
    }
  }

  /**
   * 创建支付订单
   * POST /api/v1/weixin/create-payment
   */
  async createPayment(params: CreatePaymentRequest): Promise<WeixinPaymentParams> {
    try {
      const url = getWeixinApiUrl('/create-payment')
      const response = await httpClient.post<WeixinPaymentParams>(url, params)

      console.log('创建支付订单成功:', response)
      return response
    } catch (error) {
      console.error('创建支付订单失败:', error)
      throw error
    }
  }

  /**
   * 查询支付状态
   * GET /api/v1/weixin/payment-status/{out_trade_no}
   */
  async getPaymentStatus(outTradeNo: string): Promise<PaymentStatusResponse> {
    try {
      const url = getWeixinApiUrl(`/payment-status/${outTradeNo}`)
      const response = await httpClient.get<PaymentStatusResponse>(url)

      console.log('查询支付状态成功:', response)
      return response
    } catch (error) {
      console.error('查询支付状态失败:', error)
      throw error
    }
  }

  /**
   * 获取支付订单列表
   * GET /api/v1/weixin/payment-orders?openid={openid}
   */
  async getPaymentOrders(customOpenid?: string): Promise<PaymentOrdersResponse> {
    try {
      const targetOpenid = customOpenid || this.getOpenid()
       if (!targetOpenid) {
        throw new Error('缺少 openid 参数')
      }
      const url = getWeixinApiUrl('/payment-orders')

      const fullUrl = `${url}?openid=${targetOpenid}}`
      const response = await httpClient.get<PaymentOrdersResponse>(fullUrl)

      console.log('获取支付订单列表成功:', response)
      return response
    } catch (error) {
      console.error('获取支付订单列表失败:', error)
      throw error
    }
  }

  /**
   * 发起微信支付
   * 封装完整的支付流程
   */
  async requestPayment(packageId: string): Promise<boolean> {
    try {
      const openid = this.getOpenid()
       if (!openid) {
        throw new Error('缺少 openid 参数')
      }

      // 创建支付订单
      const payParams = await this.createPayment({
        openid: openid,
        packageId: packageId
      })

      console.log('支付参数:', payParams)

      // 调用微信支付
      return new Promise((resolve, reject) => {
        // #ifdef MP-WEIXIN
        // 使用uniapp的微信支付API
        uni.requestPayment({
          provider: 'wxpay',
          appid: payParams.appId,
          timeStamp: payParams.timeStamp,
          nonceStr: payParams.nonceStr,
          package: payParams.package,
          signType: payParams.signType,
          paySign: payParams.paySign,
          success: (res: any) => {
            console.log('支付成功:', res)
            uni.showToast({
              title: '支付成功',
              icon: 'success',
              duration: 2000
            })
            resolve(true)
          },
          fail: (err: any) => {
            console.error('支付失败:', err)
            if (err.errMsg && err.errMsg.includes('cancel')) {
              uni.showToast({
                title: '支付已取消',
                icon: 'none',
                duration: 2000
              })
            } else {
              uni.showToast({
                title: '支付失败',
                icon: 'none',
                duration: 2000
              })
            }
            reject(err)
          }
        })
        // #endif

        // #ifndef MP-WEIXIN
        // 非微信小程序环境，模拟支付成功
        console.log('非微信环境，模拟支付成功')
        uni.showToast({
          title: '模拟支付成功',
          icon: 'success',
          duration: 2000
        })
        resolve(true)
        // #endif
      })
    } catch (error) {
      console.error('发起支付失败:', error)
      uni.showToast({
        title: '支付失败',
        icon: 'none',
        duration: 2000
      })
      throw error
    }
  }

  /**
   * 获取全局配置
   * GET /api/v1/weixin/global-config
   */
  async getGlobalConfig(): Promise<GlobalConfig> {
    try {
      const url = getWeixinApiUrl('/global-config')
      const response = await httpClient.get<GlobalConfigResponse>(url)

      if (response.success && response.data) {
        console.log('获取全局配置成功:', response.data)
        return response.data
      } else {
        throw new Error(response.message || '获取全局配置失败')
      }
    } catch (error) {
      console.error('获取全局配置失败:', error)

      // 返回默认配置
      const defaultConfig: GlobalConfig = {
        backgroundMusic: {
          mainPageUrl: '/static/audio/bg-main.mp3',
          gamePageUrl: '/static/audio/bg-game.mp3',
          menuPageUrl: '/static/audio/bg-menu.mp3'
        },
        helpPage: {
          url: '/pages/help/index',
          title: '游戏帮助'
        },
        app: {
          name: '英语单词游戏',
          version: '1.0.0',
          description: '挑战你的词汇量，提升英语水平！'
        }
      }

      console.log('使用默认全局配置:', defaultConfig)
      return defaultConfig
    }
  }
}

// 创建并导出微信API服务实例
export const weixinApi = new WeixinApiService()

// 初始化 openid
weixinApi.initializeOpenid()

export default weixinApi
