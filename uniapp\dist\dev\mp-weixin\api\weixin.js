"use strict";
const common_vendor = require("../common/vendor.js");
const api_config = require("./config.js");
const api_request = require("./request.js");
const api_utils = require("./utils.js");
let openid = "";
class WeixinApiService {
  /**
   * 设置 openid
   */
  setOpenid(newOpenid) {
    openid = newOpenid;
    common_vendor.index.setStorageSync(api_config.STORAGE_KEYS.USER_OPENID, newOpenid);
    console.log("设置 openid:", newOpenid);
  }
  /**
   * 获取当前 openid
   */
  getOpenid() {
    if (!openid || openid === "") {
      const localOpenid = common_vendor.index.getStorageSync(api_config.STORAGE_KEYS.USER_OPENID);
      if (localOpenid) {
        openid = localOpenid;
      } else {
        openid = "";
      }
    }
    return openid;
  }
  /**
   * 初始化 openid（从本地存储恢复）
   */
  initializeOpenid() {
    const localOpenid = common_vendor.index.getStorageSync(api_config.STORAGE_KEYS.USER_OPENID);
    if (localOpenid) {
      openid = localOpenid;
      console.log("从本地存储恢复 openid:", localOpenid);
    }
  }
  /**
   * 用户绑定接口
   * POST /api/v1/weixin/user/bind
   */
  async bindUser(params) {
    try {
      const url = api_config.getWeixinApiUrl("/user/bind");
      const requestParams = {
        openid: this.getOpenid(),
        phone: (params == null ? void 0 : params.phone) || "",
        // 默认手机号，实际应该从用户授权获取
        nickname: (params == null ? void 0 : params.nickname) || "微信用户",
        avatarUrl: (params == null ? void 0 : params.avatarUrl) || ""
      };
      const response = await api_request.httpClient.post(url, requestParams);
      common_vendor.index.setStorageSync(api_config.STORAGE_KEYS.USER_INFO, JSON.stringify(response));
      this.setOpenid(requestParams.openid);
      console.log("用户绑定成功:", response);
      return response;
    } catch (error) {
      console.error("用户绑定失败:", error);
      throw error;
    }
  }
  /**
   * 获取用户信息（使用通用 openid）
   * GET /api/v1/weixin/user/info?openid={openid}
   */
  async getUserInfo(customOpenid) {
    try {
      const targetOpenid = customOpenid || this.getOpenid();
      if (!targetOpenid) {
        throw new Error("缺少 openid 参数");
      }
      const url = api_config.getWeixinApiUrl(`/user/info?openid=${targetOpenid}`);
      const response = await api_request.httpClient.get(url);
      common_vendor.index.setStorageSync(api_config.STORAGE_KEYS.USER_INFO, JSON.stringify(response));
      console.log("获取用户信息成功:", response);
      return response;
    } catch (error) {
      console.error("获取用户信息失败:", error);
      throw error;
    }
  }
  /**
   * 根据用户ID获取用户信息
   * GET /api/v1/weixin/user/{id}/info
   */
  async getUserInfoById(userId) {
    try {
      const url = api_config.getWeixinApiUrl(`/user/${userId}/info`);
      const response = await api_request.httpClient.get(url);
      console.log("根据ID获取用户信息成功:", response);
      return response;
    } catch (error) {
      console.error("根据ID获取用户信息失败:", error);
      throw error;
    }
  }
  /**
   * 获取关卡列表（包含用户进度，使用通用 openid）
   * GET /api/v1/weixin/levels?openid={openid}
   */
  async getLevels(customOpenid) {
    try {
      const targetOpenid = customOpenid || this.getOpenid();
      if (!targetOpenid) {
        throw new Error("缺少 openid 参数");
      }
      const url = api_config.getWeixinApiUrl(`/levels?openid=${targetOpenid}`);
      const response = await api_request.httpClient.get(url);
      console.log("获取关卡列表成功:", response);
      return response;
    } catch (error) {
      console.error("获取关卡列表失败:", error);
      throw error;
    }
  }
  /**
   * 强制刷新微信登录状态
   * 清除本地缓存并重新获取code
   */
  async forceRefreshWeixinSession() {
    return new Promise((resolve) => {
      common_vendor.index.checkSession({
        success: () => {
          console.log("检测到旧的微信会话，准备清除...");
          resolve();
        },
        fail: () => {
          console.log("没有旧的微信会话");
          resolve();
        }
      });
    });
  }
  /**
   * 微信登录获取code（uniapp官方API）
   * 根据官方文档：https://uniapp.dcloud.net.cn/api/plugins/login.html#login
   */
  async weixinLogin() {
    await this.forceRefreshWeixinSession();
    return new Promise((resolve, reject) => {
      console.log("开始获取新的微信登录code...");
      common_vendor.index.login({
        provider: "weixin",
        timeout: 1e4,
        // 设置超时时间
        success: (loginRes) => {
          console.log("微信登录成功:", loginRes);
          if (loginRes.code) {
            console.log("获取到新的微信code:", loginRes.code);
            resolve({
              code: loginRes.code
            });
          } else {
            reject(new Error("未获取到登录凭证"));
          }
        },
        fail: (error) => {
          console.error("微信登录失败:", error);
          reject(new Error(`微信登录失败: ${error.errMsg || "未知错误"}`));
        }
      });
    });
  }
  /**
   * 完整的微信登录流程（按照WEIXIN_LOGIN_FLOW.md实现）
   * 只使用uni.login获取code，其他信息作为可选参数
   * POST /api/v1/weixin/login
   */
  async performWeixinLogin(userInfo) {
    try {
      console.log("开始微信登录流程，清除旧的登录状态...");
      this.clearLocalUserData();
      const loginResult = await this.weixinLogin();
      console.log("获取微信登录凭证成功:", loginResult.code);
      const loginRequest = {
        code: loginResult.code,
        nickname: (userInfo == null ? void 0 : userInfo.nickname) || "微信用户",
        // 可选
        avatarUrl: (userInfo == null ? void 0 : userInfo.avatarUrl) || ""
        // 可选
      };
      if (userInfo == null ? void 0 : userInfo.phone) {
        loginRequest.phone = userInfo.phone;
      }
      console.log("发送登录请求:", loginRequest);
      const url = api_config.getWeixinApiUrl("/login");
      console.log("请求URL:", url);
      console.log("请求头信息:", {
        "Content-Type": "application/json",
        "User-Agent": "uniapp-weixin-client"
      });
      const response = await api_request.httpClient.post(url, loginRequest);
      if (response.status === "success") {
        common_vendor.index.setStorageSync(api_config.STORAGE_KEYS.USER_INFO, JSON.stringify(response.userInfo));
        this.setOpenid(response.openid);
        console.log("微信登录成功:", response);
      } else if (response.status === "need_bind") {
        this.setOpenid(response.openid);
        console.log("需要绑定手机号:", response);
      }
      return response;
    } catch (error) {
      console.error("微信登录流程失败:", error);
      if (error && typeof error === "object") {
        const errorObj = error;
        console.error("错误详情:", {
          message: errorObj.message,
          statusCode: errorObj.statusCode,
          details: errorObj.details,
          stack: errorObj.stack
        });
        if (errorObj.statusCode) {
          console.error(`HTTP错误 ${errorObj.statusCode}:`, errorObj.details);
          if (errorObj.statusCode === 500) {
            console.error("服务器内部错误，可能的原因:");
            console.error("1. 数据库连接问题");
            console.error("2. 微信API配置问题");
            console.error("3. 服务器代码异常");
            console.error("4. 环境变量配置错误");
          }
        }
      }
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes("code已经被使用") || errorMessage.includes("code been used") || errorMessage.includes("40163")) {
        console.log("检测到code重复使用错误，清除本地状态...");
        this.clearLocalUserData();
        throw new Error("登录状态已过期，请重新尝试");
      }
      throw error;
    }
  }
  /**
   * 绑定手机号完成注册
   * POST /api/v1/weixin/bind-phone
   */
  async bindPhone(params) {
    try {
      const url = api_config.getWeixinApiUrl("/bind-phone");
      const response = await api_request.httpClient.post(url, params);
      if (response.status === "success") {
        common_vendor.index.setStorageSync(api_config.STORAGE_KEYS.USER_INFO, JSON.stringify(response.userInfo));
        this.setOpenid(response.openid);
        console.log("手机号绑定成功:", response.userInfo);
        return response.userInfo;
      } else {
        throw new Error("绑定失败");
      }
    } catch (error) {
      console.error("绑定手机号失败:", error);
      throw error;
    }
  }
  /**
   * 检查微信配置状态
   * GET /api/v1/weixin/config
   */
  async checkWeixinConfig() {
    try {
      const url = api_config.getWeixinApiUrl("/config");
      const response = await api_request.httpClient.get(url);
      console.log("微信配置状态:", response);
      return response;
    } catch (error) {
      console.error("检查微信配置失败:", error);
      throw error;
    }
  }
  /**
   * 检查登录状态是否过期（根据官方文档）
   * 仅在微信小程序环境中可用
   */
  async checkSession() {
    return new Promise((resolve) => {
      common_vendor.index.checkSession({
        success: () => {
          console.log("登录状态有效");
          resolve(true);
        },
        fail: () => {
          console.log("登录状态已过期");
          resolve(false);
        }
      });
    });
  }
  /**
   * 获取分享配置
   * GET /api/v1/weixin/share-config
   */
  async getShareConfig(params) {
    try {
      const baseUrl = api_config.getWeixinApiUrl("/share-config");
      const url = params ? api_utils.appendQueryToUrl(baseUrl, {
        page: params.page,
        levelId: params.levelId,
        userId: params.userId
      }) : baseUrl;
      const response = await api_request.httpClient.get(url);
      console.log("获取分享配置成功:", response);
      return response;
    } catch (error) {
      console.error("获取分享配置失败:", error);
      const defaultConfig = {
        title: "英语单词游戏 - 挑战你的词汇量！",
        path: "/pages/index/index",
        imageUrl: "/static/share-logo.png",
        desc: "快来挑战英语单词游戏，提升你的词汇量！",
        summary: "和朋友一起学英语，看谁的词汇量更丰富！"
      };
      console.log("使用默认分享配置:", defaultConfig);
      return defaultConfig;
    }
  }
  /**
   * 获取每日状态
   * GET /api/v1/weixin/daily-status?openid={openid}
   */
  async getDailyStatus(customOpenid) {
    try {
      const targetOpenid = customOpenid || this.getOpenid();
      if (!targetOpenid) {
        throw new Error("缺少 openid 参数");
      }
      const url = api_config.getWeixinApiUrl("/daily-status");
      const fullUrl = `${url}?openid=${targetOpenid}`;
      const response = await api_request.httpClient.get(fullUrl);
      console.log("获取每日状态成功:", response);
      return response;
    } catch (error) {
      console.error("获取每日状态失败:", error);
      throw error;
    }
  }
  /**
   * 用户分享（新版本）
   * POST /api/v1/weixin/share
   */
  async shareForReward(customOpenid) {
    try {
      const targetOpenid = customOpenid || this.getOpenid();
      if (!targetOpenid) {
        throw new Error("缺少 openid 参数");
      }
      const url = api_config.getWeixinApiUrl("/share");
      const response = await api_request.httpClient.post(url, {
        openid: targetOpenid
      });
      console.log("分享奖励获取成功:", response);
      return response;
    } catch (error) {
      console.error("分享奖励获取失败:", error);
      throw error;
    }
  }
  /**
   * 分享后获取奖励（旧版本，保持兼容性）
   * POST /api/v1/weixin/share
   */
  async getShareReward() {
    try {
      const targetOpenid = this.getOpenid();
      if (!targetOpenid) {
        throw new Error("缺少 openid 参数");
      }
      const url = api_config.getWeixinApiUrl("/share");
      const response = await api_request.httpClient.post(url, {
        openid: targetOpenid
      });
      console.log("分享奖励获取成功:", response);
      if (response.userInfo) {
        common_vendor.index.setStorageSync(api_config.STORAGE_KEYS.USER_INFO, JSON.stringify(response.userInfo));
      }
      return response;
    } catch (error) {
      console.error("获取分享奖励失败:", error);
      throw error;
    }
  }
  /**
   * 检查用户是否已绑定（使用通用 openid）
   */
  async checkUserBound(customOpenid) {
    try {
      await this.getUserInfo(customOpenid);
      return true;
    } catch (error) {
      if (error.statusCode === 404) {
        return false;
      }
      throw error;
    }
  }
  /**
   * 从本地存储获取用户信息
   */
  getLocalUserInfo() {
    try {
      const userInfoStr = common_vendor.index.getStorageSync(api_config.STORAGE_KEYS.USER_INFO);
      if (userInfoStr) {
        return JSON.parse(userInfoStr);
      }
      return null;
    } catch (error) {
      console.error("获取本地用户信息失败:", error);
      return null;
    }
  }
  /**
   * 从本地存储获取openid（兼容旧方法）
   */
  getLocalOpenid() {
    try {
      return this.getOpenid() || null;
    } catch (error) {
      console.error("获取本地openid失败:", error);
      return null;
    }
  }
  /**
   * 清除本地用户数据
   */
  clearLocalUserData() {
    try {
      common_vendor.index.removeStorageSync(api_config.STORAGE_KEYS.USER_INFO);
      common_vendor.index.removeStorageSync(api_config.STORAGE_KEYS.USER_OPENID);
      openid = "";
      console.log("清除本地用户数据成功");
    } catch (error) {
      console.error("清除本地用户数据失败:", error);
    }
  }
  /**
   * 刷新用户信息（使用通用 openid）
   */
  async refreshUserInfo() {
    try {
      return await this.getUserInfo();
    } catch (error) {
      console.error("刷新用户信息失败:", error);
      return null;
    }
  }
  /**
   * 获取关卡详情（包含词组信息）
   * GET /api/v1/level/{id}/with-phrases
   */
  async getLevelDetail(levelId, customOpenid) {
    try {
      const targetOpenid = customOpenid || this.getOpenid();
      if (!targetOpenid) {
        throw new Error("缺少 openid 参数");
      }
      const url = api_config.getWeixinApiUrl(`/level/${levelId}?openid=${targetOpenid}`);
      const response = await api_request.httpClient.get(url);
      console.log("获取关卡详情成功:", response);
      return response;
    } catch (error) {
      console.error("获取关卡详情失败:", error);
      throw error;
    }
  }
  /**
   * 用户完成关卡
   * POST /api/v1/weixin/level/complete
   */
  async completeLevel(userId, levelId) {
    try {
      const targetOpenid = this.getOpenid();
      if (!targetOpenid) {
        throw new Error("缺少 openid 参数");
      }
      const url = api_config.getWeixinApiUrl(`/level/complete`);
      const requestData = {
        levelId,
        openid: targetOpenid
      };
      const response = await api_request.httpClient.post(url, requestData);
      console.log("关卡完成记录成功:", response);
      return response;
    } catch (error) {
      console.error("记录关卡完成失败:", error);
      throw error;
    }
  }
  /**
   * 用户完成关卡（旧版本，返回UserInfo）
   * POST /api/v1/weixin/level/complete
   */
  async completeLevelLegacy(userId, levelId) {
    try {
      const response = await this.completeLevel(userId, levelId);
      const userInfo = {
        id: response.userId,
        maskedPhone: "",
        // 需要从其他地方获取
        unlockedLevels: response.unlockedLevels,
        completedLevelIds: [],
        // 需要从其他地方获取
        totalGames: 0,
        // 需要从其他地方获取
        totalCompletions: response.totalCompletions,
        lastPlayTime: (/* @__PURE__ */ new Date()).toISOString(),
        createdAt: "",
        // 需要从其他地方获取
        isVip: response.isVip,
        dailyUnlockLimit: response.dailyUnlockLimit,
        dailyUnlockCount: response.dailyUnlockCount,
        dailyShared: false,
        // 需要从其他地方获取
        lastPlayDate: (/* @__PURE__ */ new Date()).toISOString().split("T")[0],
        totalShares: 0
        // 需要从其他地方获取
      };
      common_vendor.index.setStorageSync(api_config.STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo));
      return userInfo;
    } catch (error) {
      console.error("记录关卡完成失败:", error);
      throw error;
    }
  }
  /**
   * 获取VIP套餐列表
   * GET /api/v1/weixin/vip-packages
   */
  async getVipPackages() {
    try {
      const url = api_config.getWeixinApiUrl("/vip-packages");
      const response = await api_request.httpClient.get(url);
      console.log("获取VIP套餐列表成功:", response);
      return response;
    } catch (error) {
      console.error("获取VIP套餐列表失败:", error);
      throw error;
    }
  }
  /**
   * 创建支付订单
   * POST /api/v1/weixin/create-payment
   */
  async createPayment(params) {
    try {
      const url = api_config.getWeixinApiUrl("/create-payment");
      const response = await api_request.httpClient.post(url, params);
      console.log("创建支付订单成功:", response);
      return response;
    } catch (error) {
      console.error("创建支付订单失败:", error);
      throw error;
    }
  }
  /**
   * 查询支付状态
   * GET /api/v1/weixin/payment-status/{out_trade_no}
   */
  async getPaymentStatus(outTradeNo) {
    try {
      const url = api_config.getWeixinApiUrl(`/payment-status/${outTradeNo}`);
      const response = await api_request.httpClient.get(url);
      console.log("查询支付状态成功:", response);
      return response;
    } catch (error) {
      console.error("查询支付状态失败:", error);
      throw error;
    }
  }
  /**
   * 获取支付订单列表
   * GET /api/v1/weixin/payment-orders?openid={openid}
   */
  async getPaymentOrders(customOpenid) {
    try {
      const targetOpenid = customOpenid || this.getOpenid();
      if (!targetOpenid) {
        throw new Error("缺少 openid 参数");
      }
      const url = api_config.getWeixinApiUrl("/payment-orders");
      const fullUrl = `${url}?openid=${targetOpenid}}`;
      const response = await api_request.httpClient.get(fullUrl);
      console.log("获取支付订单列表成功:", response);
      return response;
    } catch (error) {
      console.error("获取支付订单列表失败:", error);
      throw error;
    }
  }
  /**
   * 发起微信支付
   * 封装完整的支付流程
   */
  async requestPayment(packageId) {
    try {
      const openid2 = this.getOpenid();
      if (!openid2) {
        throw new Error("缺少 openid 参数");
      }
      const payParams = await this.createPayment({
        openid: openid2,
        packageId
      });
      console.log("支付参数:", payParams);
      return new Promise((resolve, reject) => {
        common_vendor.index.requestPayment({
          provider: "wxpay",
          appid: payParams.appId,
          timeStamp: payParams.timeStamp,
          nonceStr: payParams.nonceStr,
          package: payParams.package,
          signType: payParams.signType,
          paySign: payParams.paySign,
          success: (res) => {
            console.log("支付成功:", res);
            common_vendor.index.showToast({
              title: "支付成功",
              icon: "success",
              duration: 2e3
            });
            resolve(true);
          },
          fail: (err) => {
            console.error("支付失败:", err);
            if (err.errMsg && err.errMsg.includes("cancel")) {
              common_vendor.index.showToast({
                title: "支付已取消",
                icon: "none",
                duration: 2e3
              });
            } else {
              common_vendor.index.showToast({
                title: "支付失败",
                icon: "none",
                duration: 2e3
              });
            }
            reject(err);
          }
        });
      });
    } catch (error) {
      console.error("发起支付失败:", error);
      common_vendor.index.showToast({
        title: "支付失败",
        icon: "none",
        duration: 2e3
      });
      throw error;
    }
  }
  /**
   * 获取全局配置
   * GET /api/v1/weixin/global-config
   */
  async getGlobalConfig() {
    try {
      const url = api_config.getWeixinApiUrl("/global-config");
      const response = await api_request.httpClient.get(url);
      if (response.success && response.data) {
        console.log("获取全局配置成功:", response.data);
        return response.data;
      } else {
        throw new Error(response.message || "获取全局配置失败");
      }
    } catch (error) {
      console.error("获取全局配置失败:", error);
      const defaultConfig = {
        backgroundMusic: {
          mainPageUrl: "/static/audio/bg-main.mp3",
          gamePageUrl: "/static/audio/bg-game.mp3",
          menuPageUrl: "/static/audio/bg-menu.mp3"
        },
        helpPage: {
          url: "/pages/help/index",
          title: "游戏帮助"
        },
        app: {
          name: "英语单词游戏",
          version: "1.0.0",
          description: "挑战你的词汇量，提升英语水平！"
        }
      };
      console.log("使用默认全局配置:", defaultConfig);
      return defaultConfig;
    }
  }
}
const weixinApi = new WeixinApiService();
weixinApi.initializeOpenid();
exports.weixinApi = weixinApi;
